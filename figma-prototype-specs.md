# Figma Prototype Specifications

## Interactive Prototype Requirements

### 1. Customer App Prototype Flow
**Main User Journey**: Splash → Login → Book → Track → Pay → History

#### Screen Transitions:
- **Splash to Login**: Auto-advance after 3s OR tap to skip
- **Login to Dashboard**: Slide up animation after successful OTP
- **Dashboard to Fare**: Slide left when "Book Now" tapped
- **Fare to Tracking**: <PERSON>lide left when "Confirm Booking" tapped
- **Tracking to Payment**: Slide up when delivery completed
- **Payment to History**: Slide right after payment success

#### Interactive Elements:
- **Language Toggle**: Instant text swap (EN ↔ हिं)
- **Location Inputs**: Show dropdown with recent/saved locations
- **Loader Type Selection**: Highlight selected option with green border
- **Map View**: Pinch to zoom, drag to pan (static map image)
- **Rating Stars**: Tap to select 1-5 stars with animation
- **Payment Methods**: Radio button selection with visual feedback

---

### 2. Driver App Prototype Flow
**Main User Journey**: Login → Dashboard → Navigate → Complete → Earnings

#### Screen Transitions:
- **<PERSON>gin to Dashboard**: Fade in after successful login
- **Dashboard to Navigation**: Slide left when "Navigate" tapped
- **Navigation to OTP**: Slide up when "Arrived" tapped
- **OTP to Dashboard**: Slide down after delivery completion
- **Dashboard to Earnings**: Slide right from bottom nav

#### Interactive Elements:
- **Online/Offline Toggle**: Switch animation with status color change
- **Active Deliveries**: Swipe left to reveal quick actions
- **Map Navigation**: Show route animation (simulated)
- **OTP Input**: Auto-focus next field, validate 4-digit code
- **Call/Chat Buttons**: Show modal with contact options

---

### 3. Vendor App Prototype Flow
**Main User Journey**: Login → Schedule → Track → Billing

#### Screen Transitions:
- **Login to Dashboard**: Slide up after successful login
- **Dashboard to Schedule**: Modal overlay from bottom
- **Schedule to Dashboard**: Modal dismiss with confirmation
- **Dashboard to Tracking**: Slide left when "Track Live" tapped
- **Tracking to Billing**: Slide right from bottom nav

#### Interactive Elements:
- **Schedule Form**: Progressive disclosure (show fields as needed)
- **Loader Type Selection**: Visual selection with size comparison
- **Date/Time Picker**: Native picker overlay
- **Live Map**: Real-time position updates (simulated)
- **Payment Status**: Color-coded status indicators

---

## Figma File Structure

### 1. Design System Page
```
📄 Design System
├── 🎨 Colors (Primary, Secondary, Status)
├── 📝 Typography (Headings, Body, Captions)
├── 🔘 Buttons (Primary, Secondary, Text)
├── 📱 Components (Cards, Inputs, Navigation)
├── 🌐 Language Variants (EN/Hindi text samples)
└── 📐 Spacing & Layout Grid
```

### 2. Customer App Pages
```
📱 Customer App
├── 🌟 Splash Screen
├── 🔐 Login/Signup
├── 🏠 Dashboard (Book Loader)
├── 💰 Fare Estimate
├── 📍 Live Tracking
├── 💳 Payment
└── 📋 History
```

### 3. Driver App Pages
```
🚛 Driver App
├── 🔐 Driver Login
├── 📊 Dashboard (Active Deliveries)
├── 🗺️ Navigation
├── ✅ Delivery Completion (OTP)
└── 💰 Earnings
```

### 4. Vendor App Pages
```
🏢 Vendor App
├── 🔐 Vendor Login
├── 📋 Schedule Dashboard
├── ➕ Schedule New Delivery
├── 📍 Live Tracking
└── 💳 Payment & Billing
```

---

## Component Library for Figma

### 1. Reusable Components
- **EV Loader Logo** (with animation variants)
- **Language Toggle** (EN/हिं states)
- **Primary Button** (Normal, Pressed, Disabled states)
- **Input Field** (Empty, Filled, Error, Focus states)
- **Loader Type Cards** (Small, Medium, Large with selection states)
- **Status Indicators** (Online, Offline, In Progress, Completed)
- **Rating Component** (0-5 stars with hover states)
- **Map Container** (with pin and route overlays)
- **Bottom Navigation** (5 tabs with active/inactive states)

### 2. Auto-Layout Frames
- **Screen Container** (375px width, auto-height)
- **Card Layout** (16px padding, 16px gap)
- **Button Row** (space-between, 16px gap)
- **Form Layout** (vertical, 20px gap)
- **List Item** (horizontal, 12px gap)

### 3. Color Styles
- **EV Green** (#00C851)
- **Electric Blue** (#007BFF)
- **Deep Charcoal** (#2C3E50)
- **Light Green** (#E8F5E8)
- **Warm Gray** (#F8F9FA)
- **Success** (#28A745)
- **Warning** (#FFC107)
- **Error** (#DC3545)

### 4. Text Styles
- **H1 Bold** (Inter Bold, 24px, Deep Charcoal)
- **H2 Bold** (Inter Bold, 20px, Deep Charcoal)
- **Body Regular** (Inter Regular, 16px, Deep Charcoal)
- **Body Small** (Inter Regular, 14px, Dark Gray)
- **Caption** (Inter Medium, 12px, Dark Gray)
- **Button Text** (Inter Bold, 16px, White)

---

## Prototype Interactions

### 1. Micro-Interactions
- **Button Press**: Scale 0.95 + shadow reduction (100ms)
- **Card Tap**: Scale 0.98 + shadow increase (150ms)
- **Toggle Switch**: Slide animation (200ms ease-out)
- **Loading States**: Pulse animation (1s infinite)
- **Success States**: Checkmark animation (300ms)

### 2. Page Transitions
- **Forward Navigation**: Slide left (300ms ease-out)
- **Back Navigation**: Slide right (300ms ease-out)
- **Modal Open**: Slide up from bottom (250ms ease-out)
- **Modal Close**: Slide down to bottom (200ms ease-in)

### 3. Smart Animate
- **Language Toggle**: Text content swap with fade
- **Status Changes**: Color transitions (400ms)
- **Progress Indicators**: Width/position changes
- **Map Updates**: Position changes for pins/routes

---

## Responsive Considerations

### 1. Screen Sizes
- **iPhone SE**: 375 × 667px
- **iPhone 12/13**: 390 × 844px
- **iPhone 12/13 Pro Max**: 428 × 926px
- **Android (Small)**: 360 × 640px
- **Android (Large)**: 412 × 915px

### 2. Safe Areas
- **iOS**: Top 44px, Bottom 34px
- **Android**: Top 24px, Bottom 16px
- **Content Area**: Adjust padding accordingly

### 3. Touch Targets
- **Minimum Size**: 44 × 44px
- **Recommended**: 48 × 48px for primary actions
- **Spacing**: 8px minimum between touch targets

---

## Handoff Specifications

### 1. Developer Assets
- **Icons**: SVG format, 24px base size
- **Images**: 2x and 3x PNG exports
- **Colors**: Hex codes with opacity values
- **Fonts**: Inter and Noto Sans Devanagari font files

### 2. Animation Specs
- **Duration**: Specified in milliseconds
- **Easing**: CSS cubic-bezier values
- **Keyframes**: Start/end states clearly defined

### 3. Spacing Values
- **Base Unit**: 8px
- **Component Padding**: 16px, 20px, 24px
- **Screen Margins**: 16px horizontal, 24px vertical
