{"extends": "@tsconfig/react-native/tsconfig.json", "compilerOptions": {"allowJs": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "isolatedModules": true, "jsx": "react-native", "lib": ["es2017"], "moduleResolution": "node", "noEmit": true, "strict": true, "target": "esnext", "baseUrl": "./src", "paths": {"@/*": ["*"], "@/components/*": ["components/*"], "@/screens/*": ["screens/*"], "@/navigation/*": ["navigation/*"], "@/services/*": ["services/*"], "@/utils/*": ["utils/*"], "@/types/*": ["types/*"], "@/assets/*": ["assets/*"], "@/hooks/*": ["hooks/*"], "@/store/*": ["store/*"]}}, "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"]}