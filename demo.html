<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EV Loader Apps - Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #F8F9FA;
            color: #2C3E50;
            line-height: 1.6;
        }

        .container {
            max-width: 375px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .header {
            background: #00C851;
            color: white;
            padding: 20px 16px;
            text-align: center;
            position: relative;
        }

        .language-toggle {
            position: absolute;
            top: 20px;
            right: 16px;
            background: rgba(255,255,255,0.2);
            border-radius: 8px;
            padding: 4px;
            display: flex;
            gap: 2px;
        }

        .lang-btn {
            background: transparent;
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
        }

        .lang-btn.active {
            background: white;
            color: #00C851;
        }

        .logo {
            font-size: 60px;
            margin-bottom: 16px;
        }

        .app-name {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .tagline {
            font-size: 16px;
            opacity: 0.9;
        }

        .content {
            padding: 24px 16px;
        }

        .welcome-section {
            margin-bottom: 32px;
        }

        .welcome-text {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .welcome-subtext {
            color: #6C757D;
            font-size: 16px;
        }

        .booking-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 24px;
        }

        .input-group {
            margin-bottom: 16px;
        }

        .input-label {
            font-size: 12px;
            font-weight: 500;
            color: #6C757D;
            margin-bottom: 4px;
            display: block;
        }

        .input-field {
            width: 100%;
            height: 56px;
            border: 1px solid #E9ECEF;
            border-radius: 12px;
            padding: 0 16px;
            font-size: 16px;
            background: white;
        }

        .input-field:focus {
            outline: none;
            border-color: #007BFF;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
        }

        .loader-types {
            margin: 24px 0;
        }

        .loader-types-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 16px;
        }

        .loader-options {
            display: flex;
            gap: 12px;
        }

        .loader-option {
            flex: 1;
            background: white;
            border: 1px solid #E9ECEF;
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
        }

        .loader-option:hover {
            border-color: #00C851;
            background: #E8F5E8;
        }

        .loader-option.selected {
            border-color: #00C851;
            background: #E8F5E8;
            border-width: 2px;
        }

        .loader-icon {
            font-size: 32px;
            margin-bottom: 8px;
        }

        .loader-label {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .loader-desc {
            font-size: 10px;
            color: #6C757D;
        }

        .btn-primary {
            width: 100%;
            height: 56px;
            background: #00C851;
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-primary:hover {
            background: #00B347;
            transform: translateY(-1px);
        }

        .btn-primary:active {
            transform: translateY(0);
        }

        .benefits-card {
            background: #E8F5E8;
            border: 1px solid #00C851;
            border-radius: 16px;
            padding: 20px;
            margin-top: 24px;
        }

        .benefits-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        .benefits-icon {
            font-size: 24px;
            margin-right: 12px;
        }

        .benefits-title {
            font-size: 16px;
            font-weight: 600;
            color: #00C851;
        }

        .benefits-text {
            color: #00C851;
            font-size: 14px;
        }

        .demo-note {
            background: #F0F8FF;
            border: 1px solid #007BFF;
            border-radius: 12px;
            padding: 16px;
            margin: 24px 0;
            text-align: center;
        }

        .demo-note-title {
            font-size: 16px;
            font-weight: 600;
            color: #007BFF;
            margin-bottom: 8px;
        }

        .demo-note-text {
            font-size: 14px;
            color: #007BFF;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .fade-in {
            animation: fadeIn 0.6s ease-out;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="language-toggle">
                <button class="lang-btn active" onclick="switchLanguage('en')">EN</button>
                <button class="lang-btn" onclick="switchLanguage('hi')">हिं</button>
            </div>
            <div class="logo">🚛⚡</div>
            <div class="app-name" id="appName">EV LOADER</div>
            <div class="tagline" id="tagline">Sustainable • Fast • Safe</div>
        </div>

        <!-- Content -->
        <div class="content">
            <!-- Demo Note -->
            <div class="demo-note fade-in">
                <div class="demo-note-title">📱 Live Demo</div>
                <div class="demo-note-text">This is a working demo of the Customer App interface. All components are interactive!</div>
            </div>

            <!-- Welcome Section -->
            <div class="welcome-section fade-in">
                <div class="welcome-text" id="welcomeText">Hi Akash! 👋</div>
                <div class="welcome-subtext" id="welcomeSubtext">Where do you need a loader?</div>
            </div>

            <!-- Booking Card -->
            <div class="booking-card fade-in">
                <!-- Pickup Location -->
                <div class="input-group">
                    <label class="input-label" id="pickupLabel">📍 Pickup Location</label>
                    <input type="text" class="input-field" id="pickupInput" placeholder="Current Location" value="Sector 18, Noida">
                </div>

                <!-- Drop Location -->
                <div class="input-group">
                    <label class="input-label" id="dropLabel">📍 Drop Location</label>
                    <input type="text" class="input-field" id="dropInput" placeholder="Where to?" value="Connaught Place, Delhi">
                </div>

                <!-- Loader Type Selector -->
                <div class="loader-types">
                    <div class="loader-types-title" id="loaderTitle">Select Loader Type</div>
                    <div class="loader-options">
                        <div class="loader-option" onclick="selectLoader('small')">
                            <div class="loader-icon">🚚</div>
                            <div class="loader-label" id="smallLabel">Small</div>
                            <div class="loader-desc" id="smallDesc">Up to 500kg</div>
                        </div>
                        <div class="loader-option selected" onclick="selectLoader('medium')">
                            <div class="loader-icon">🚛</div>
                            <div class="loader-label" id="mediumLabel">Medium</div>
                            <div class="loader-desc" id="mediumDesc">Up to 1000kg</div>
                        </div>
                        <div class="loader-option" onclick="selectLoader('large')">
                            <div class="loader-icon">🛻</div>
                            <div class="loader-label" id="largeLabel">Large</div>
                            <div class="loader-desc" id="largeDesc">Up to 2000kg</div>
                        </div>
                    </div>
                </div>

                <!-- Book Now Button -->
                <button class="btn-primary" id="bookBtn" onclick="bookNow()">BOOK NOW</button>
            </div>

            <!-- EV Benefits -->
            <div class="benefits-card fade-in">
                <div class="benefits-header">
                    <div class="benefits-icon">🌱</div>
                    <div class="benefits-title" id="benefitsTitle">EV Benefits</div>
                </div>
                <div class="benefits-text" id="benefitsText">Zero emissions • Eco-friendly • Cost effective</div>
            </div>
        </div>
    </div>

    <script>
        let currentLanguage = 'en';
        
        const translations = {
            en: {
                appName: 'EV LOADER',
                tagline: 'Sustainable • Fast • Safe',
                welcomeText: 'Hi Akash! 👋',
                welcomeSubtext: 'Where do you need a loader?',
                pickupLabel: '📍 Pickup Location',
                dropLabel: '📍 Drop Location',
                loaderTitle: 'Select Loader Type',
                smallLabel: 'Small',
                mediumLabel: 'Medium',
                largeLabel: 'Large',
                smallDesc: 'Up to 500kg',
                mediumDesc: 'Up to 1000kg',
                largeDesc: 'Up to 2000kg',
                bookBtn: 'BOOK NOW',
                benefitsTitle: 'EV Benefits',
                benefitsText: 'Zero emissions • Eco-friendly • Cost effective'
            },
            hi: {
                appName: 'EV लोडर',
                tagline: 'टिकाऊ • तेज़ • सुरक्षित',
                welcomeText: 'नमस्ते अकाश! 👋',
                welcomeSubtext: 'आपको लोडर कहाँ चाहिए?',
                pickupLabel: '📍 पिकअप स्थान',
                dropLabel: '📍 ड्रॉप स्थान',
                loaderTitle: 'लोडर प्रकार चुनें',
                smallLabel: 'छोटा',
                mediumLabel: 'मध्यम',
                largeLabel: 'बड़ा',
                smallDesc: '500 किग्रा तक',
                mediumDesc: '1000 किग्रा तक',
                largeDesc: '2000 किग्रा तक',
                bookBtn: 'अभी बुक करें',
                benefitsTitle: 'EV लाभ',
                benefitsText: 'शून्य उत्सर्जन • पर्यावरण अनुकूल • लागत प्रभावी'
            }
        };

        function switchLanguage(lang) {
            currentLanguage = lang;
            
            // Update active button
            document.querySelectorAll('.lang-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // Update text content
            const t = translations[lang];
            Object.keys(t).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    element.textContent = t[key];
                }
            });
        }

        function selectLoader(type) {
            document.querySelectorAll('.loader-option').forEach(option => {
                option.classList.remove('selected');
            });
            event.currentTarget.classList.add('selected');
        }

        function bookNow() {
            const pickup = document.getElementById('pickupInput').value;
            const drop = document.getElementById('dropInput').value;
            
            if (!pickup || !drop) {
                alert(currentLanguage === 'en' ? 'Please enter both pickup and drop locations' : 'कृपया पिकअप और ड्रॉप दोनों स्थान दर्ज करें');
                return;
            }
            
            // Simulate booking process
            const btn = document.getElementById('bookBtn');
            btn.textContent = currentLanguage === 'en' ? 'BOOKING...' : 'बुकिंग...';
            btn.style.background = '#6C757D';
            
            setTimeout(() => {
                alert(currentLanguage === 'en' ? 
                    '🎉 Booking confirmed! Driver will arrive in 15 minutes.' : 
                    '🎉 बुकिंग पुष्ट! ड्राइवर 15 मिनट में पहुंचेगा।'
                );
                btn.textContent = currentLanguage === 'en' ? 'BOOK NOW' : 'अभी बुक करें';
                btn.style.background = '#00C851';
            }, 2000);
        }

        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Add fade-in animation to elements
            const elements = document.querySelectorAll('.fade-in');
            elements.forEach((el, index) => {
                el.style.animationDelay = `${index * 0.1}s`;
            });
        });
    </script>
</body>
</html>
