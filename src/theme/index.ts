// EV Loader App Design System - Main Theme Export
import { colors, darkColors, getStatusColor, getLoaderTypeColor, getPaymentStatusColor, withOpacity } from './colors';
import { typography, fontFamilies, fontSizes, lineHeights, fontWeights, getTypographyStyle } from './typography';
import { spacing, layout, breakpoints, grid, durations, easings, zIndex, getSpacing, getResponsiveSpacing } from './spacing';

// Main theme object
export const theme = {
  colors,
  typography,
  spacing,
  layout,
  breakpoints,
  grid,
  durations,
  easings,
  zIndex,
} as const;

// Dark theme variant
export const darkTheme = {
  ...theme,
  colors: darkColors,
} as const;

// Theme utilities
export const themeUtils = {
  getStatusColor,
  getLoaderTypeColor,
  getPaymentStatusColor,
  withOpacity,
  getTypographyStyle,
  getSpacing,
  getResponsiveSpacing,
} as const;

// Component style presets
export const componentStyles = {
  // Button styles
  button: {
    primary: {
      backgroundColor: colors.primary,
      borderRadius: spacing.borderRadius.md,
      height: layout.component.button,
      paddingHorizontal: spacing.buttonPadding,
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
      ...spacing.shadow.base,
    },
    secondary: {
      backgroundColor: 'transparent',
      borderWidth: 2,
      borderColor: colors.secondary,
      borderRadius: spacing.borderRadius.md,
      height: layout.component.button,
      paddingHorizontal: spacing.buttonPadding,
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
    },
    text: {
      backgroundColor: 'transparent',
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
    },
  },
  
  // Input styles
  input: {
    default: {
      borderWidth: 1,
      borderColor: colors.gray200,
      borderRadius: spacing.borderRadius.md,
      height: layout.component.input,
      paddingHorizontal: spacing.inputPadding,
      backgroundColor: colors.white,
      ...typography.input,
    },
    focused: {
      borderColor: colors.secondary,
      ...spacing.shadow.sm,
    },
    error: {
      borderColor: colors.error,
    },
  },
  
  // Card styles
  card: {
    default: {
      backgroundColor: colors.white,
      borderRadius: spacing.borderRadius.lg,
      padding: spacing.cardPadding,
      ...spacing.shadow.base,
    },
    elevated: {
      backgroundColor: colors.white,
      borderRadius: spacing.borderRadius.lg,
      padding: spacing.cardPadding,
      ...spacing.shadow.md,
    },
  },
  
  // Screen container styles
  screen: {
    default: {
      flex: 1,
      backgroundColor: colors.background,
      paddingHorizontal: spacing.screenPadding,
    },
    centered: {
      flex: 1,
      backgroundColor: colors.background,
      paddingHorizontal: spacing.screenPadding,
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
    },
  },
  
  // Navigation styles
  navigation: {
    tabBar: {
      height: spacing.tabBarHeight,
      backgroundColor: colors.white,
      borderTopWidth: 1,
      borderTopColor: colors.gray200,
      paddingBottom: spacing.sm,
    },
    header: {
      height: spacing.headerHeight,
      backgroundColor: colors.white,
      borderBottomWidth: 1,
      borderBottomColor: colors.gray200,
    },
  },
  
  // List item styles
  listItem: {
    default: {
      backgroundColor: colors.white,
      paddingHorizontal: spacing.base,
      paddingVertical: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.gray200,
      minHeight: layout.component.listItem,
    },
    card: {
      backgroundColor: colors.white,
      marginHorizontal: spacing.base,
      marginVertical: spacing.sm,
      borderRadius: spacing.borderRadius.md,
      padding: spacing.base,
      ...spacing.shadow.sm,
    },
  },
  
  // Status indicator styles
  statusIndicator: {
    online: {
      backgroundColor: colors.online,
      width: 12,
      height: 12,
      borderRadius: spacing.borderRadius.full,
    },
    offline: {
      backgroundColor: colors.offline,
      width: 12,
      height: 12,
      borderRadius: spacing.borderRadius.full,
    },
  },
  
  // Badge styles
  badge: {
    default: {
      backgroundColor: colors.primary,
      borderRadius: spacing.borderRadius.full,
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs,
      minWidth: 20,
      height: 20,
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
    },
    small: {
      backgroundColor: colors.primary,
      borderRadius: spacing.borderRadius.full,
      paddingHorizontal: spacing.xs,
      paddingVertical: 2,
      minWidth: 16,
      height: 16,
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
    },
  },
} as const;

// Export individual theme parts
export {
  colors,
  darkColors,
  typography,
  fontFamilies,
  fontSizes,
  lineHeights,
  fontWeights,
  spacing,
  layout,
  breakpoints,
  grid,
  durations,
  easings,
  zIndex,
};

// Export utility functions
export {
  getStatusColor,
  getLoaderTypeColor,
  getPaymentStatusColor,
  withOpacity,
  getTypographyStyle,
  getSpacing,
  getResponsiveSpacing,
};

// Type exports
export type Theme = typeof theme;
export type Colors = typeof colors;
export type Typography = typeof typography;
export type Spacing = typeof spacing;

// Default export
export default theme;
