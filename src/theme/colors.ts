// EV Loader App Design System - Colors
export const colors = {
  // Primary Colors
  primary: '#00C851', // EV Green
  secondary: '#007BFF', // Electric Blue
  text: '#2C3E50', // Deep Charcoal
  
  // Secondary Colors
  primaryLight: '#E8F5E8', // Light Green
  secondaryLight: '#F0F8FF', // Soft Blue
  background: '#F8F9FA', // Warm Gray
  textSecondary: '#6C757D', // Dark Gray
  
  // Status Colors
  success: '#28A745',
  warning: '#FFC107',
  error: '#DC3545',
  info: '#17A2B8',
  
  // Neutral Colors
  white: '#FFFFFF',
  black: '#000000',
  gray100: '#F8F9FA',
  gray200: '#E9ECEF',
  gray300: '#DEE2E6',
  gray400: '#CED4DA',
  gray500: '#ADB5BD',
  gray600: '#6C757D',
  gray700: '#495057',
  gray800: '#343A40',
  gray900: '#212529',
  
  // Transparent Colors
  overlay: 'rgba(0, 0, 0, 0.5)',
  overlayLight: 'rgba(0, 0, 0, 0.3)',
  shadowColor: 'rgba(0, 0, 0, 0.1)',
  
  // Gradient Colors
  primaryGradient: ['#00C851', '#28A745'],
  secondaryGradient: ['#007BFF', '#17A2B8'],
  
  // App Specific Colors
  online: '#28A745',
  offline: '#6C757D',
  inProgress: '#FFC107',
  completed: '#00C851',
  cancelled: '#DC3545',
  
  // Loader Type Colors
  smallLoader: '#17A2B8',
  mediumLoader: '#FFC107',
  largeLoader: '#DC3545',
  
  // Map Colors
  routeColor: '#007BFF',
  pickupMarker: '#00C851',
  dropMarker: '#DC3545',
  driverMarker: '#FFC107',
  
  // Rating Colors
  starActive: '#FFC107',
  starInactive: '#E9ECEF',
  
  // Payment Status Colors
  paymentPending: '#FFC107',
  paymentCompleted: '#28A745',
  paymentFailed: '#DC3545',
  paymentRefunded: '#17A2B8',
} as const;

// Color utility functions
export const getStatusColor = (status: string): string => {
  switch (status) {
    case 'online':
    case 'completed':
    case 'delivered':
      return colors.success;
    case 'offline':
    case 'cancelled':
      return colors.error;
    case 'in_progress':
    case 'pending':
      return colors.warning;
    case 'confirmed':
    case 'driver_assigned':
      return colors.info;
    default:
      return colors.textSecondary;
  }
};

export const getLoaderTypeColor = (type: string): string => {
  switch (type) {
    case 'small':
      return colors.smallLoader;
    case 'medium':
      return colors.mediumLoader;
    case 'large':
      return colors.largeLoader;
    default:
      return colors.textSecondary;
  }
};

export const getPaymentStatusColor = (status: string): string => {
  switch (status) {
    case 'completed':
      return colors.paymentCompleted;
    case 'pending':
      return colors.paymentPending;
    case 'failed':
      return colors.paymentFailed;
    case 'refunded':
      return colors.paymentRefunded;
    default:
      return colors.textSecondary;
  }
};

// Opacity variations
export const withOpacity = (color: string, opacity: number): string => {
  // Convert hex to rgba
  const hex = color.replace('#', '');
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);
  
  return `rgba(${r}, ${g}, ${b}, ${opacity})`;
};

// Dark mode colors (for future implementation)
export const darkColors = {
  ...colors,
  background: '#121212',
  text: '#FFFFFF',
  textSecondary: '#B3B3B3',
  white: '#1E1E1E',
  gray100: '#2C2C2C',
  gray200: '#3C3C3C',
  gray300: '#4C4C4C',
  overlay: 'rgba(255, 255, 255, 0.1)',
  overlayLight: 'rgba(255, 255, 255, 0.05)',
};

export type ColorKeys = keyof typeof colors;
