import { Platform } from 'react-native';

// Font families
export const fontFamilies = {
  primary: Platform.select({
    ios: 'Inter',
    android: 'Inter-Regular',
  }),
  primaryBold: Platform.select({
    ios: 'Inter-Bold',
    android: 'Inter-Bold',
  }),
  primaryMedium: Platform.select({
    ios: 'Inter-Medium',
    android: 'Inter-Medium',
  }),
  hindi: Platform.select({
    ios: 'NotoSansDevanagari',
    android: 'NotoSansDevanagari-Regular',
  }),
  hindiBold: Platform.select({
    ios: 'NotoSansDevanagari-Bold',
    android: 'NotoSansDevanagari-Bold',
  }),
  hindiMedium: Platform.select({
    ios: 'NotoSansDevanagari-Medium',
    android: 'NotoSansDevanagari-Medium',
  }),
} as const;

// Font sizes
export const fontSizes = {
  xs: 10,
  sm: 12,
  base: 14,
  md: 16,
  lg: 18,
  xl: 20,
  '2xl': 24,
  '3xl': 28,
  '4xl': 32,
  '5xl': 36,
} as const;

// Line heights
export const lineHeights = {
  xs: 14,
  sm: 16,
  base: 20,
  md: 22,
  lg: 24,
  xl: 28,
  '2xl': 32,
  '3xl': 36,
  '4xl': 40,
  '5xl': 44,
} as const;

// Font weights
export const fontWeights = {
  normal: '400' as const,
  medium: '500' as const,
  semibold: '600' as const,
  bold: '700' as const,
};

// Typography styles
export const typography = {
  // Headings
  h1: {
    fontFamily: fontFamilies.primaryBold,
    fontSize: fontSizes['2xl'],
    lineHeight: lineHeights['2xl'],
    fontWeight: fontWeights.bold,
  },
  h2: {
    fontFamily: fontFamilies.primaryBold,
    fontSize: fontSizes.xl,
    lineHeight: lineHeights.xl,
    fontWeight: fontWeights.bold,
  },
  h3: {
    fontFamily: fontFamilies.primaryBold,
    fontSize: fontSizes.lg,
    lineHeight: lineHeights.lg,
    fontWeight: fontWeights.bold,
  },
  h4: {
    fontFamily: fontFamilies.primaryMedium,
    fontSize: fontSizes.md,
    lineHeight: lineHeights.md,
    fontWeight: fontWeights.semibold,
  },
  
  // Body text
  bodyLarge: {
    fontFamily: fontFamilies.primary,
    fontSize: fontSizes.md,
    lineHeight: lineHeights.md,
    fontWeight: fontWeights.normal,
  },
  body: {
    fontFamily: fontFamilies.primary,
    fontSize: fontSizes.base,
    lineHeight: lineHeights.base,
    fontWeight: fontWeights.normal,
  },
  bodySmall: {
    fontFamily: fontFamilies.primary,
    fontSize: fontSizes.sm,
    lineHeight: lineHeights.sm,
    fontWeight: fontWeights.normal,
  },
  
  // Captions
  caption: {
    fontFamily: fontFamilies.primaryMedium,
    fontSize: fontSizes.sm,
    lineHeight: lineHeights.sm,
    fontWeight: fontWeights.medium,
  },
  captionSmall: {
    fontFamily: fontFamilies.primaryMedium,
    fontSize: fontSizes.xs,
    lineHeight: lineHeights.xs,
    fontWeight: fontWeights.medium,
  },
  
  // Button text
  button: {
    fontFamily: fontFamilies.primaryBold,
    fontSize: fontSizes.md,
    lineHeight: lineHeights.md,
    fontWeight: fontWeights.bold,
  },
  buttonLarge: {
    fontFamily: fontFamilies.primaryBold,
    fontSize: fontSizes.md,
    lineHeight: lineHeights.md,
    fontWeight: fontWeights.bold,
  },
  buttonSmall: {
    fontFamily: fontFamilies.primaryMedium,
    fontSize: fontSizes.sm,
    lineHeight: lineHeights.sm,
    fontWeight: fontWeights.medium,
  },
  
  // Input text
  input: {
    fontFamily: fontFamilies.primary,
    fontSize: fontSizes.md,
    lineHeight: lineHeights.md,
    fontWeight: fontWeights.normal,
  },
  inputLabel: {
    fontFamily: fontFamilies.primaryMedium,
    fontSize: fontSizes.sm,
    lineHeight: lineHeights.sm,
    fontWeight: fontWeights.medium,
  },
  
  // Navigation
  tabLabel: {
    fontFamily: fontFamilies.primaryMedium,
    fontSize: fontSizes.xs,
    lineHeight: lineHeights.xs,
    fontWeight: fontWeights.medium,
  },
  
  // Hindi variants
  h1Hindi: {
    fontFamily: fontFamilies.hindiBold,
    fontSize: fontSizes['2xl'],
    lineHeight: lineHeights['2xl'],
    fontWeight: fontWeights.bold,
  },
  h2Hindi: {
    fontFamily: fontFamilies.hindiBold,
    fontSize: fontSizes.xl,
    lineHeight: lineHeights.xl,
    fontWeight: fontWeights.bold,
  },
  h3Hindi: {
    fontFamily: fontFamilies.hindiBold,
    fontSize: fontSizes.lg,
    lineHeight: lineHeights.lg,
    fontWeight: fontWeights.bold,
  },
  bodyHindi: {
    fontFamily: fontFamilies.hindi,
    fontSize: fontSizes.base,
    lineHeight: lineHeights.base,
    fontWeight: fontWeights.normal,
  },
  captionHindi: {
    fontFamily: fontFamilies.hindiMedium,
    fontSize: fontSizes.sm,
    lineHeight: lineHeights.sm,
    fontWeight: fontWeights.medium,
  },
  buttonHindi: {
    fontFamily: fontFamilies.hindiBold,
    fontSize: fontSizes.md,
    lineHeight: lineHeights.md,
    fontWeight: fontWeights.bold,
  },
} as const;

// Utility function to get typography style based on language
export const getTypographyStyle = (
  style: keyof typeof typography,
  language: 'en' | 'hi' = 'en'
) => {
  if (language === 'hi') {
    const hindiStyle = `${style}Hindi` as keyof typeof typography;
    return typography[hindiStyle] || typography[style];
  }
  return typography[style];
};

// Text transform utilities
export const textTransforms = {
  uppercase: 'uppercase' as const,
  lowercase: 'lowercase' as const,
  capitalize: 'capitalize' as const,
  none: 'none' as const,
};

// Text alignment utilities
export const textAligns = {
  left: 'left' as const,
  center: 'center' as const,
  right: 'right' as const,
  justify: 'justify' as const,
};

export type TypographyKeys = keyof typeof typography;
export type FontSizeKeys = keyof typeof fontSizes;
export type LineHeightKeys = keyof typeof lineHeights;
