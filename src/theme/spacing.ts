// EV Loader App Design System - Spacing
// Base unit: 8px

export const spacing = {
  // Base spacing units
  xs: 4,
  sm: 8,
  md: 12,
  base: 16,
  lg: 20,
  xl: 24,
  '2xl': 32,
  '3xl': 40,
  '4xl': 48,
  '5xl': 64,
  '6xl': 80,
  
  // Component specific spacing
  buttonPadding: 16,
  cardPadding: 20,
  screenPadding: 16,
  sectionSpacing: 24,
  
  // Input spacing
  inputPadding: 16,
  inputMargin: 12,
  
  // Navigation spacing
  tabBarHeight: 80,
  headerHeight: 56,
  
  // Safe area spacing
  safeAreaTop: 44, // iOS
  safeAreaBottom: 34, // iOS
  safeAreaBottomAndroid: 16,
  
  // Touch targets
  minTouchTarget: 44,
  recommendedTouchTarget: 48,
  
  // Border radius
  borderRadius: {
    none: 0,
    sm: 4,
    base: 8,
    md: 12,
    lg: 16,
    xl: 20,
    '2xl': 24,
    full: 9999,
  },
  
  // Shadow depths
  shadow: {
    sm: {
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 1,
    },
    base: {
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
    },
    md: {
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.15,
      shadowRadius: 8,
      elevation: 4,
    },
    lg: {
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.2,
      shadowRadius: 16,
      elevation: 8,
    },
    xl: {
      shadowOffset: { width: 0, height: 12 },
      shadowOpacity: 0.25,
      shadowRadius: 24,
      elevation: 12,
    },
  },
} as const;

// Layout utilities
export const layout = {
  // Container widths
  container: {
    sm: 640,
    md: 768,
    lg: 1024,
    xl: 1280,
  },
  
  // Screen dimensions (common mobile sizes)
  screen: {
    iphoneSE: { width: 375, height: 667 },
    iphone12: { width: 390, height: 844 },
    iphone12ProMax: { width: 428, height: 926 },
    androidSmall: { width: 360, height: 640 },
    androidLarge: { width: 412, height: 915 },
  },
  
  // Component heights
  component: {
    button: 56,
    input: 56,
    card: 'auto',
    listItem: 72,
    tabBar: 80,
    header: 56,
    searchBar: 48,
  },
  
  // Flex utilities
  flex: {
    1: 1,
    2: 2,
    3: 3,
    4: 4,
    none: 0,
  },
} as const;

// Responsive breakpoints
export const breakpoints = {
  sm: 480,
  md: 768,
  lg: 1024,
  xl: 1280,
} as const;

// Grid system
export const grid = {
  columns: 12,
  gutter: spacing.base,
  margin: spacing.base,
} as const;

// Animation durations
export const durations = {
  fast: 150,
  normal: 300,
  slow: 500,
  slower: 800,
} as const;

// Animation easing
export const easings = {
  linear: 'linear',
  easeIn: 'ease-in',
  easeOut: 'ease-out',
  easeInOut: 'ease-in-out',
  spring: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
} as const;

// Z-index layers
export const zIndex = {
  hide: -1,
  base: 0,
  docked: 10,
  dropdown: 1000,
  sticky: 1100,
  banner: 1200,
  overlay: 1300,
  modal: 1400,
  popover: 1500,
  skipLink: 1600,
  toast: 1700,
  tooltip: 1800,
} as const;

// Utility functions
export const getSpacing = (multiplier: number): number => {
  return spacing.sm * multiplier;
};

export const getResponsiveSpacing = (
  screenWidth: number,
  baseSpacing: number = spacing.base
): number => {
  if (screenWidth < breakpoints.sm) {
    return baseSpacing * 0.75;
  } else if (screenWidth < breakpoints.md) {
    return baseSpacing;
  } else {
    return baseSpacing * 1.25;
  }
};

export type SpacingKeys = keyof typeof spacing;
export type BorderRadiusKeys = keyof typeof spacing.borderRadius;
export type ShadowKeys = keyof typeof spacing.shadow;
