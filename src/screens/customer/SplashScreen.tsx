import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { colors, typography, spacing } from '@/theme';
import { LanguageToggle } from '@/components';

interface SplashScreenProps {
  onComplete: () => void;
}

const { width, height } = Dimensions.get('window');

export const SplashScreen: React.FC<SplashScreenProps> = ({ onComplete }) => {
  const { t, i18n } = useTranslation();
  const [fadeAnim] = useState(new Animated.Value(0));
  const [scaleAnim] = useState(new Animated.Value(0.8));
  const [dotsAnim] = useState(new Animated.Value(0));

  useEffect(() => {
    // Start animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();

    // Animate loading dots
    const animateDots = () => {
      Animated.sequence([
        Animated.timing(dotsAnim, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.timing(dotsAnim, {
          toValue: 0,
          duration: 500,
          useNativeDriver: true,
        }),
      ]).start(() => animateDots());
    };

    animateDots();

    // Auto-advance after 3 seconds
    const timer = setTimeout(() => {
      onComplete();
    }, 3000);

    return () => clearTimeout(timer);
  }, [fadeAnim, scaleAnim, dotsAnim, onComplete]);

  const handleLanguageChange = (language: 'en' | 'hi') => {
    i18n.changeLanguage(language);
  };

  const renderLoadingDots = () => {
    return (
      <View style={styles.dotsContainer}>
        {[0, 1, 2].map((index) => (
          <Animated.View
            key={index}
            style={[
              styles.dot,
              {
                opacity: dotsAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0.3, 1],
                }),
                transform: [
                  {
                    scale: dotsAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [0.8, 1.2],
                    }),
                  },
                ],
              },
            ]}
          />
        ))}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {/* Language Toggle */}
      <View style={styles.languageContainer}>
        <LanguageToggle
          currentLanguage={i18n.language as 'en' | 'hi'}
          onLanguageChange={handleLanguageChange}
        />
      </View>

      {/* Main Content */}
      <View style={styles.content}>
        <Animated.View
          style={[
            styles.logoContainer,
            {
              opacity: fadeAnim,
              transform: [{ scale: scaleAnim }],
            },
          ]}
        >
          {/* Logo */}
          <Text style={styles.logoIcon}>🚛⚡</Text>
          
          {/* App Name */}
          <Text style={styles.appName}>{t('customer.appName')}</Text>
          
          {/* Tagline */}
          <Text style={styles.tagline}>{t('customer.tagline')}</Text>
        </Animated.View>

        {/* Loading Animation */}
        <Animated.View
          style={[
            styles.loadingContainer,
            { opacity: fadeAnim },
          ]}
        >
          {renderLoadingDots()}
          
          {/* Loading Message */}
          <Text style={styles.loadingMessage}>
            {t('customer.connectingMessage')}
          </Text>
        </Animated.View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  languageContainer: {
    position: 'absolute',
    top: spacing.safeAreaTop + spacing.base,
    right: spacing.base,
    zIndex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: spacing['5xl'],
  },
  logoIcon: {
    fontSize: 80,
    marginBottom: spacing.xl,
  },
  appName: {
    ...typography.h1,
    color: colors.white,
    fontSize: 32,
    fontWeight: '700',
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  tagline: {
    ...typography.body,
    color: colors.white,
    opacity: 0.9,
    textAlign: 'center',
    fontSize: 16,
  },
  loadingContainer: {
    alignItems: 'center',
  },
  dotsContainer: {
    flexDirection: 'row',
    marginBottom: spacing.xl,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.white,
    marginHorizontal: 4,
  },
  loadingMessage: {
    ...typography.caption,
    color: colors.white,
    opacity: 0.8,
    textAlign: 'center',
    fontSize: 14,
    lineHeight: 20,
  },
});

export default SplashScreen;
