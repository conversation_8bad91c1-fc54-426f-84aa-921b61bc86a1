import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { colors, typography, spacing } from '@/theme';
import { Button, Input, Card, Header, LoaderTypeSelector, LanguageToggle } from '@/components';
import { LoaderType, Address } from '@/types';

interface DashboardScreenProps {
  onBookingConfirm: (bookingData: any) => void;
}

export const DashboardScreen: React.FC<DashboardScreenProps> = ({
  onBookingConfirm,
}) => {
  const { t, i18n } = useTranslation();
  const [pickupLocation, setPickupLocation] = useState('');
  const [dropLocation, setDropLocation] = useState('');
  const [selectedLoaderType, setSelectedLoaderType] = useState<LoaderType>('medium');
  const [loading, setLoading] = useState(false);

  const userName = 'Akash'; // This would come from user context/state

  const handleLocationSelect = (type: 'pickup' | 'drop') => {
    // This would open a location picker modal
    Alert.alert('Location Picker', `Select ${type} location`);
  };

  const handleBookNow = async () => {
    if (!pickupLocation.trim()) {
      Alert.alert('Error', 'Please enter pickup location');
      return;
    }

    if (!dropLocation.trim()) {
      Alert.alert('Error', 'Please enter drop location');
      return;
    }

    setLoading(true);
    try {
      // Simulate API call to get fare estimate
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const bookingData = {
        pickupAddress: {
          id: '1',
          label: 'Pickup',
          address: pickupLocation,
          location: { latitude: 28.5355, longitude: 77.3910 },
          type: 'other' as const,
        },
        dropAddress: {
          id: '2',
          label: 'Drop',
          address: dropLocation,
          location: { latitude: 28.6139, longitude: 77.2090 },
          type: 'other' as const,
        },
        loaderType: selectedLoaderType,
      };

      onBookingConfirm(bookingData);
    } catch (error) {
      Alert.alert('Error', 'Failed to process booking. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleLanguageChange = (language: 'en' | 'hi') => {
    i18n.changeLanguage(language);
  };

  const renderQuickActions = () => (
    <View style={styles.quickActionsContainer}>
      <Text style={styles.sectionTitle}>Quick Actions</Text>
      <View style={styles.quickActionsRow}>
        <TouchableOpacity style={styles.quickActionCard}>
          <Text style={styles.quickActionIcon}>📋</Text>
          <Text style={styles.quickActionText}>{t('customer.recentBookings')}</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.quickActionCard}>
          <Text style={styles.quickActionIcon}>⚡</Text>
          <Text style={styles.quickActionText}>Quick Book</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <Header
        title={t('customer.appName')}
        leftIcon={<Text style={styles.menuIcon}>☰</Text>}
        rightIcon={
          <View style={styles.headerRight}>
            <TouchableOpacity style={styles.notificationButton}>
              <Text style={styles.notificationIcon}>🔔</Text>
            </TouchableOpacity>
            <LanguageToggle
              currentLanguage={i18n.language as 'en' | 'hi'}
              onLanguageChange={handleLanguageChange}
            />
          </View>
        }
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.content}
        showsVerticalScrollIndicator={false}
      >
        {/* Welcome Section */}
        <View style={styles.welcomeSection}>
          <Text style={styles.welcomeText}>
            {t('customer.hiUser', { name: userName })}
          </Text>
          <Text style={styles.welcomeSubtext}>
            {t('customer.whereNeedLoader')}
          </Text>
        </View>

        {/* Booking Form */}
        <Card style={styles.bookingCard}>
          {/* Pickup Location */}
          <TouchableOpacity
            style={styles.locationInput}
            onPress={() => handleLocationSelect('pickup')}
          >
            <View style={styles.locationIcon}>
              <Text style={styles.locationIconText}>📍</Text>
            </View>
            <View style={styles.locationContent}>
              <Text style={styles.locationLabel}>{t('customer.pickupLocation')}</Text>
              <Text style={styles.locationValue}>
                {pickupLocation || t('customer.currentLocation')}
              </Text>
            </View>
            <Text style={styles.dropdownIcon}>▼</Text>
          </TouchableOpacity>

          {/* Drop Location */}
          <Input
            label={t('customer.dropLocation')}
            value={dropLocation}
            onChangeText={setDropLocation}
            placeholder={t('customer.whereTo')}
            leftIcon={<Text style={styles.inputIcon}>📍</Text>}
            style={styles.dropInput}
          />

          {/* Loader Type Selector */}
          <LoaderTypeSelector
            selectedType={selectedLoaderType}
            onTypeSelect={setSelectedLoaderType}
            style={styles.loaderSelector}
          />

          {/* Book Now Button */}
          <Button
            title={t('customer.bookNow')}
            onPress={handleBookNow}
            loading={loading}
            style={styles.bookButton}
          />
        </Card>

        {/* Quick Actions */}
        {renderQuickActions()}

        {/* EV Benefits */}
        <Card style={styles.benefitsCard}>
          <View style={styles.benefitsHeader}>
            <Text style={styles.benefitsIcon}>🌱</Text>
            <Text style={styles.benefitsTitle}>EV Benefits</Text>
          </View>
          <Text style={styles.benefitsText}>
            Zero emissions • Eco-friendly • Cost effective
          </Text>
        </Card>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    paddingHorizontal: spacing.base,
    paddingBottom: spacing.xl,
  },
  menuIcon: {
    fontSize: 20,
    color: colors.text,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  notificationButton: {
    padding: spacing.xs,
  },
  notificationIcon: {
    fontSize: 20,
  },
  welcomeSection: {
    marginVertical: spacing.lg,
  },
  welcomeText: {
    ...typography.h2,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  welcomeSubtext: {
    ...typography.body,
    color: colors.textSecondary,
  },
  bookingCard: {
    marginBottom: spacing.lg,
  },
  locationInput: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.base,
    paddingHorizontal: spacing.base,
    borderWidth: 1,
    borderColor: colors.gray200,
    borderRadius: spacing.borderRadius.md,
    backgroundColor: colors.white,
    marginBottom: spacing.base,
  },
  locationIcon: {
    marginRight: spacing.md,
  },
  locationIconText: {
    fontSize: 20,
  },
  locationContent: {
    flex: 1,
  },
  locationLabel: {
    ...typography.caption,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  locationValue: {
    ...typography.body,
    color: colors.text,
  },
  dropdownIcon: {
    ...typography.body,
    color: colors.textSecondary,
  },
  inputIcon: {
    fontSize: 20,
  },
  dropInput: {
    marginBottom: spacing.base,
  },
  loaderSelector: {
    marginBottom: spacing.lg,
  },
  bookButton: {
    marginTop: spacing.base,
  },
  quickActionsContainer: {
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    ...typography.h4,
    color: colors.text,
    marginBottom: spacing.md,
  },
  quickActionsRow: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  quickActionCard: {
    flex: 1,
    backgroundColor: colors.white,
    padding: spacing.base,
    borderRadius: spacing.borderRadius.md,
    alignItems: 'center',
    ...spacing.shadow.sm,
  },
  quickActionIcon: {
    fontSize: 24,
    marginBottom: spacing.sm,
  },
  quickActionText: {
    ...typography.caption,
    color: colors.text,
    textAlign: 'center',
  },
  benefitsCard: {
    backgroundColor: colors.primaryLight,
    borderColor: colors.primary,
    borderWidth: 1,
  },
  benefitsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  benefitsIcon: {
    fontSize: 24,
    marginRight: spacing.sm,
  },
  benefitsTitle: {
    ...typography.h4,
    color: colors.primary,
  },
  benefitsText: {
    ...typography.body,
    color: colors.primary,
  },
});

export default DashboardScreen;
