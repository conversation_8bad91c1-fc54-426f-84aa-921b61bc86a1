import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { colors, typography, spacing } from '@/theme';
import { <PERSON><PERSON>, <PERSON>, Header } from '@/components';
import { LoaderType, Address } from '@/types';

interface FareEstimateScreenProps {
  route: {
    params: {
      pickupAddress: Address;
      dropAddress: Address;
      loaderType: LoaderType;
    };
  };
  navigation: any;
  onConfirmBooking: (bookingData: any) => void;
}

interface FareBreakdown {
  baseFare: number;
  distanceCharge: number;
  timeCharge: number;
  subtotal: number;
  gst: number;
  total: number;
  distance: number;
  estimatedTime: number;
}

export const FareEstimateScreen: React.FC<FareEstimateScreenProps> = ({
  route,
  navigation,
  onConfirmBooking,
}) => {
  const { t } = useTranslation();
  const { pickupAddress, dropAddress, loaderType } = route.params;
  const [fareBreakdown, setFareBreakdown] = useState<FareBreakdown | null>(null);
  const [loading, setLoading] = useState(true);
  const [confirming, setConfirming] = useState(false);

  useEffect(() => {
    calculateFare();
  }, []);

  const calculateFare = async () => {
    try {
      // Simulate API call to calculate fare
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock fare calculation based on loader type
      const baseRates = {
        small: { base: 100, perKm: 8, perMin: 1 },
        medium: { base: 150, perKm: 10, perMin: 1.5 },
        large: { base: 200, perKm: 12, perMin: 2 },
      };

      const rate = baseRates[loaderType];
      const distance = 28; // Mock distance in km
      const estimatedTime = 45; // Mock time in minutes

      const baseFare = rate.base;
      const distanceCharge = distance * rate.perKm;
      const timeCharge = estimatedTime * rate.perMin;
      const subtotal = baseFare + distanceCharge + timeCharge;
      const gst = Math.round(subtotal * 0.18);
      const total = subtotal + gst;

      setFareBreakdown({
        baseFare,
        distanceCharge,
        timeCharge,
        subtotal,
        gst,
        total,
        distance,
        estimatedTime,
      });
    } catch (error) {
      Alert.alert('Error', 'Failed to calculate fare. Please try again.');
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  const handleConfirmBooking = async () => {
    if (!fareBreakdown) return;

    setConfirming(true);
    try {
      // Simulate booking confirmation API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const bookingData = {
        id: `BK${Date.now()}`,
        pickupAddress,
        dropAddress,
        loaderType,
        fareAmount: fareBreakdown.total,
        estimatedDistance: fareBreakdown.distance,
        estimatedDuration: fareBreakdown.estimatedTime,
        status: 'confirmed',
        createdAt: new Date().toISOString(),
      };

      onConfirmBooking(bookingData);
    } catch (error) {
      Alert.alert('Error', 'Failed to confirm booking. Please try again.');
    } finally {
      setConfirming(false);
    }
  };

  const renderFareBreakdown = () => {
    if (!fareBreakdown) return null;

    return (
      <Card style={styles.fareCard}>
        <Text style={styles.fareTitle}>💰 {t('customer.fareBreakdown')}:</Text>
        
        <View style={styles.fareRow}>
          <Text style={styles.fareLabel}>Base Fare</Text>
          <Text style={styles.fareValue}>₹{fareBreakdown.baseFare}</Text>
        </View>
        
        <View style={styles.fareRow}>
          <Text style={styles.fareLabel}>Distance ({fareBreakdown.distance}km)</Text>
          <Text style={styles.fareValue}>₹{fareBreakdown.distanceCharge}</Text>
        </View>
        
        <View style={styles.fareRow}>
          <Text style={styles.fareLabel}>Time Charge</Text>
          <Text style={styles.fareValue}>₹{fareBreakdown.timeCharge}</Text>
        </View>
        
        <View style={styles.divider} />
        
        <View style={styles.fareRow}>
          <Text style={styles.fareLabel}>Subtotal</Text>
          <Text style={styles.fareValue}>₹{fareBreakdown.subtotal}</Text>
        </View>
        
        <View style={styles.fareRow}>
          <Text style={styles.fareLabel}>GST (18%)</Text>
          <Text style={styles.fareValue}>₹{fareBreakdown.gst}</Text>
        </View>
        
        <View style={styles.divider} />
        
        <View style={styles.fareRow}>
          <Text style={styles.totalLabel}>Total Amount</Text>
          <Text style={styles.totalValue}>₹{fareBreakdown.total}</Text>
        </View>
      </Card>
    );
  };

  const renderTripDetails = () => (
    <Card style={styles.tripCard}>
      <View style={styles.locationRow}>
        <Text style={styles.locationIcon}>📍</Text>
        <View style={styles.locationDetails}>
          <Text style={styles.locationLabel}>Pickup:</Text>
          <Text style={styles.locationText}>{pickupAddress.address}</Text>
        </View>
      </View>
      
      <View style={styles.locationRow}>
        <Text style={styles.locationIcon}>📍</Text>
        <View style={styles.locationDetails}>
          <Text style={styles.locationLabel}>Drop:</Text>
          <Text style={styles.locationText}>{dropAddress.address}</Text>
        </View>
      </View>
      
      <View style={styles.locationRow}>
        <Text style={styles.locationIcon}>🚛</Text>
        <View style={styles.locationDetails}>
          <Text style={styles.locationLabel}>Loader Type:</Text>
          <Text style={styles.locationText}>{loaderType.charAt(0).toUpperCase() + loaderType.slice(1)} Loader</Text>
        </View>
      </View>
      
      {fareBreakdown && (
        <>
          <View style={styles.locationRow}>
            <Text style={styles.locationIcon}>📏</Text>
            <View style={styles.locationDetails}>
              <Text style={styles.locationLabel}>Distance:</Text>
              <Text style={styles.locationText}>{fareBreakdown.distance} km</Text>
            </View>
          </View>
          
          <View style={styles.locationRow}>
            <Text style={styles.locationIcon}>⏱️</Text>
            <View style={styles.locationDetails}>
              <Text style={styles.locationLabel}>Est. Time:</Text>
              <Text style={styles.locationText}>{fareBreakdown.estimatedTime} mins</Text>
            </View>
          </View>
        </>
      )}
    </Card>
  );

  return (
    <View style={styles.container}>
      <Header
        title={t('customer.fareDetails')}
        showBackButton
        onLeftPress={() => navigation.goBack()}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.content}
        showsVerticalScrollIndicator={false}
      >
        {/* Trip Details */}
        {renderTripDetails()}

        {/* Fare Breakdown */}
        {loading ? (
          <Card style={styles.loadingCard}>
            <Text style={styles.loadingText}>Calculating fare...</Text>
          </Card>
        ) : (
          renderFareBreakdown()
        )}

        {/* EV Benefits */}
        <Card style={styles.benefitsCard}>
          <Text style={styles.benefitsText}>
            ⚡ EV Delivery - Zero Emission
          </Text>
          <Text style={styles.benefitsSubtext}>
            Eco-friendly delivery with our electric vehicles
          </Text>
        </Card>

        {/* Confirm Button */}
        {!loading && (
          <Button
            title={t('customer.confirmBooking')}
            onPress={handleConfirmBooking}
            loading={confirming}
            style={styles.confirmButton}
          />
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    paddingHorizontal: spacing.base,
    paddingBottom: spacing.xl,
  },
  tripCard: {
    marginBottom: spacing.base,
  },
  locationRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: spacing.md,
  },
  locationIcon: {
    fontSize: 20,
    marginRight: spacing.md,
    marginTop: 2,
  },
  locationDetails: {
    flex: 1,
  },
  locationLabel: {
    ...typography.caption,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  locationText: {
    ...typography.body,
    color: colors.text,
  },
  fareCard: {
    marginBottom: spacing.base,
  },
  fareTitle: {
    ...typography.h4,
    color: colors.text,
    marginBottom: spacing.base,
  },
  fareRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  fareLabel: {
    ...typography.body,
    color: colors.text,
  },
  fareValue: {
    ...typography.body,
    color: colors.text,
    fontWeight: '600',
  },
  totalLabel: {
    ...typography.h4,
    color: colors.text,
  },
  totalValue: {
    ...typography.h4,
    color: colors.primary,
    fontWeight: '700',
  },
  divider: {
    height: 1,
    backgroundColor: colors.gray200,
    marginVertical: spacing.sm,
  },
  loadingCard: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
    marginBottom: spacing.base,
  },
  loadingText: {
    ...typography.body,
    color: colors.textSecondary,
  },
  benefitsCard: {
    backgroundColor: colors.primaryLight,
    borderColor: colors.primary,
    borderWidth: 1,
    marginBottom: spacing.lg,
  },
  benefitsText: {
    ...typography.h4,
    color: colors.primary,
    marginBottom: spacing.xs,
  },
  benefitsSubtext: {
    ...typography.body,
    color: colors.primary,
  },
  confirmButton: {
    marginTop: spacing.base,
  },
});

export default FareEstimateScreen;
