import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { colors, typography, spacing, componentStyles } from '@/theme';
import { But<PERSON>, Input, Header, LanguageToggle } from '@/components';

interface LoginScreenProps {
  onLoginSuccess: () => void;
  onBack: () => void;
}

export const LoginScreen: React.FC<LoginScreenProps> = ({
  onLoginSuccess,
  onBack,
}) => {
  const { t, i18n } = useTranslation();
  const [phone, setPhone] = useState('');
  const [otp, setOtp] = useState('');
  const [showOtpInput, setShowOtpInput] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleGetOtp = async () => {
    if (!phone || phone.length < 10) {
      Alert.alert('Error', 'Please enter a valid phone number');
      return;
    }

    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      setShowOtpInput(true);
      Alert.alert('Success', 'OTP sent to your phone number');
    } catch (error) {
      Alert.alert('Error', 'Failed to send OTP. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyOtp = async () => {
    if (!otp || otp.length < 4) {
      Alert.alert('Error', 'Please enter a valid OTP');
      return;
    }

    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      onLoginSuccess();
    } catch (error) {
      Alert.alert('Error', 'Invalid OTP. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleLogin = async () => {
    setLoading(true);
    try {
      // Simulate Google login
      await new Promise(resolve => setTimeout(resolve, 2000));
      onLoginSuccess();
    } catch (error) {
      Alert.alert('Error', 'Google login failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleEmailLogin = async () => {
    Alert.alert('Coming Soon', 'Email login will be available soon!');
  };

  const handleLanguageChange = (language: 'en' | 'hi') => {
    i18n.changeLanguage(language);
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <Header
        title=""
        showBackButton
        onLeftPress={onBack}
        rightIcon={
          <LanguageToggle
            currentLanguage={i18n.language as 'en' | 'hi'}
            onLanguageChange={handleLanguageChange}
          />
        }
        transparent
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.content}
        keyboardShouldPersistTaps="handled"
      >
        {/* Logo */}
        <View style={styles.logoContainer}>
          <Text style={styles.logoIcon}>🚛⚡</Text>
        </View>

        {/* Welcome Text */}
        <View style={styles.welcomeContainer}>
          <Text style={styles.welcomeTitle}>{t('auth.welcomeBack')}</Text>
          <Text style={styles.welcomeSubtitle}>{t('auth.loginToBook')}</Text>
        </View>

        {/* Phone Input */}
        <View style={styles.formContainer}>
          <Input
            label={t('auth.phoneNumber')}
            value={phone}
            onChangeText={setPhone}
            placeholder="+91 |_______________"
            keyboardType="phone-pad"
            maxLength={10}
            leftIcon={<Text style={styles.phoneIcon}>📱</Text>}
            disabled={showOtpInput}
          />

          {/* OTP Input */}
          {showOtpInput && (
            <Input
              label="Enter OTP"
              value={otp}
              onChangeText={setOtp}
              placeholder="Enter 4-digit OTP"
              keyboardType="number-pad"
              maxLength={4}
              leftIcon={<Text style={styles.phoneIcon}>🔒</Text>}
            />
          )}

          {/* Primary Button */}
          {!showOtpInput ? (
            <Button
              title={t('auth.getOtp')}
              onPress={handleGetOtp}
              loading={loading}
              style={styles.primaryButton}
            />
          ) : (
            <Button
              title="Verify OTP"
              onPress={handleVerifyOtp}
              loading={loading}
              style={styles.primaryButton}
            />
          )}

          {/* Divider */}
          <View style={styles.dividerContainer}>
            <View style={styles.dividerLine} />
            <Text style={styles.dividerText}>OR</Text>
            <View style={styles.dividerLine} />
          </View>

          {/* Social Login Buttons */}
          <Button
            title={t('auth.continueWithEmail')}
            variant="secondary"
            onPress={handleEmailLogin}
            icon={<Text style={styles.socialIcon}>📧</Text>}
            style={styles.socialButton}
          />

          <Button
            title={t('auth.continueWithGoogle')}
            variant="secondary"
            onPress={handleGoogleLogin}
            icon={<Text style={styles.socialIcon}>🔍</Text>}
            style={styles.socialButton}
          />
        </View>

        {/* Sign Up Link */}
        <View style={styles.signupContainer}>
          <Text style={styles.signupText}>
            {t('auth.newUser')} 
            <Text style={styles.signupLink}> {t('auth.signUpHere')}</Text>
          </Text>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    flexGrow: 1,
    paddingHorizontal: spacing.base,
    paddingBottom: spacing.xl,
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: spacing.xl,
    marginBottom: spacing.lg,
  },
  logoIcon: {
    fontSize: 60,
  },
  welcomeContainer: {
    alignItems: 'center',
    marginBottom: spacing['2xl'],
  },
  welcomeTitle: {
    ...typography.h1,
    color: colors.text,
    marginBottom: spacing.sm,
    textAlign: 'center',
  },
  welcomeSubtitle: {
    ...typography.body,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  formContainer: {
    marginBottom: spacing.xl,
  },
  phoneIcon: {
    fontSize: 20,
  },
  primaryButton: {
    marginTop: spacing.base,
  },
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: spacing.xl,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: colors.gray300,
  },
  dividerText: {
    ...typography.caption,
    color: colors.textSecondary,
    marginHorizontal: spacing.base,
  },
  socialButton: {
    marginBottom: spacing.md,
  },
  socialIcon: {
    fontSize: 18,
  },
  signupContainer: {
    alignItems: 'center',
    marginTop: 'auto',
  },
  signupText: {
    ...typography.body,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  signupLink: {
    color: colors.secondary,
    fontWeight: '600',
  },
});

export default LoginScreen;
