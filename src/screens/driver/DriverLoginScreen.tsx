import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { colors, typography, spacing } from '@/theme';
import { Button, Input, LanguageToggle } from '@/components';

interface DriverLoginScreenProps {
  onLoginSuccess: () => void;
}

export const DriverLoginScreen: React.FC<DriverLoginScreenProps> = ({
  onLoginSuccess,
}) => {
  const { t, i18n } = useTranslation();
  const [driverId, setDriverId] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);

  const handleLogin = async () => {
    if (!driverId.trim()) {
      Alert.alert('Error', 'Please enter your Driver ID or Phone');
      return;
    }

    if (!password.trim()) {
      Alert.alert('Error', 'Please enter your password');
      return;
    }

    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      onLoginSuccess();
    } catch (error) {
      Alert.alert('Error', 'Invalid credentials. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleForgotPassword = () => {
    Alert.alert('Forgot Password', 'Password reset link will be sent to your registered email/phone.');
  };

  const handleRegister = () => {
    Alert.alert('Register as Driver', 'Please contact our support team to register as a driver partner.');
  };

  const handleLanguageChange = (language: 'en' | 'hi') => {
    i18n.changeLanguage(language);
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      {/* Language Toggle */}
      <View style={styles.languageContainer}>
        <LanguageToggle
          currentLanguage={i18n.language as 'en' | 'hi'}
          onLanguageChange={handleLanguageChange}
        />
      </View>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.content}
        keyboardShouldPersistTaps="handled"
      >
        {/* Logo */}
        <View style={styles.logoContainer}>
          <Text style={styles.logoIcon}>🚛⚡</Text>
          <Text style={styles.appName}>EV LOADER</Text>
          <Text style={styles.appSubtitle}>{t('driver.driverPartner')}</Text>
        </View>

        {/* Welcome Text */}
        <View style={styles.welcomeContainer}>
          <Text style={styles.welcomeText}>{t('driver.startEarning')}</Text>
        </View>

        {/* Login Form */}
        <View style={styles.formContainer}>
          <Input
            label={t('driver.driverId')}
            value={driverId}
            onChangeText={setDriverId}
            placeholder="Driver ID or Phone Number"
            leftIcon={<Text style={styles.inputIcon}>📱</Text>}
            autoCapitalize="none"
          />

          <Input
            label="Password"
            value={password}
            onChangeText={setPassword}
            placeholder="Enter your password"
            secureTextEntry
            leftIcon={<Text style={styles.inputIcon}>🔒</Text>}
          />

          <Button
            title={t('auth.login')}
            onPress={handleLogin}
            loading={loading}
            style={styles.loginButton}
          />

          {/* Forgot Password */}
          <Button
            title="Forgot Password?"
            variant="text"
            onPress={handleForgotPassword}
            style={styles.forgotButton}
          />
        </View>

        {/* Register Section */}
        <View style={styles.registerContainer}>
          <Button
            title="Register as Driver"
            variant="secondary"
            onPress={handleRegister}
            style={styles.registerButton}
          />
          
          <Text style={styles.supportText}>
            Need help? Contact Support
          </Text>
          <Text style={styles.supportNumber}>+91-XXXXXXXXX</Text>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  languageContainer: {
    position: 'absolute',
    top: spacing.safeAreaTop + spacing.base,
    right: spacing.base,
    zIndex: 1,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    flexGrow: 1,
    paddingHorizontal: spacing.base,
    paddingTop: spacing.safeAreaTop + spacing['2xl'],
    paddingBottom: spacing.xl,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: spacing['2xl'],
  },
  logoIcon: {
    fontSize: 80,
    marginBottom: spacing.base,
  },
  appName: {
    ...typography.h1,
    color: colors.text,
    fontSize: 28,
    fontWeight: '700',
    marginBottom: spacing.xs,
  },
  appSubtitle: {
    ...typography.h4,
    color: colors.primary,
    fontWeight: '600',
  },
  welcomeContainer: {
    alignItems: 'center',
    marginBottom: spacing['2xl'],
  },
  welcomeText: {
    ...typography.body,
    color: colors.textSecondary,
    textAlign: 'center',
    fontSize: 16,
  },
  formContainer: {
    marginBottom: spacing.xl,
  },
  inputIcon: {
    fontSize: 20,
  },
  loginButton: {
    marginTop: spacing.base,
  },
  forgotButton: {
    marginTop: spacing.md,
    alignSelf: 'center',
  },
  registerContainer: {
    alignItems: 'center',
    marginTop: 'auto',
  },
  registerButton: {
    marginBottom: spacing.lg,
    width: '100%',
  },
  supportText: {
    ...typography.caption,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.xs,
  },
  supportNumber: {
    ...typography.caption,
    color: colors.secondary,
    fontWeight: '600',
    textAlign: 'center',
  },
});

export default DriverLoginScreen;
