import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { colors, typography, spacing, getLoaderTypeColor } from '@/theme';
import { LoaderType } from '@/types';

interface LoaderTypeSelectorProps {
  selectedType: LoaderType;
  onTypeSelect: (type: LoaderType) => void;
  style?: ViewStyle;
  disabled?: boolean;
}

interface LoaderTypeOption {
  type: LoaderType;
  label: string;
  labelHindi: string;
  icon: string;
  description: string;
  descriptionHindi: string;
  capacity: string;
  capacityHindi: string;
}

const loaderTypes: LoaderTypeOption[] = [
  {
    type: 'small',
    label: 'Small',
    labelHindi: 'छोटा',
    icon: '🚚',
    description: 'Up to 500kg',
    descriptionHindi: '500 किग्रा तक',
    capacity: '2-3 boxes',
    capacityHindi: '2-3 बॉक्स',
  },
  {
    type: 'medium',
    label: 'Medium',
    labelHindi: 'मध्यम',
    icon: '🚛',
    description: 'Up to 1000kg',
    descriptionHindi: '1000 किग्रा तक',
    capacity: '5-8 boxes',
    capacityHindi: '5-8 बॉक्स',
  },
  {
    type: 'large',
    label: 'Large',
    labelHindi: 'बड़ा',
    icon: '🛻',
    description: 'Up to 2000kg',
    descriptionHindi: '2000 किग्रा तक',
    capacity: '10+ boxes',
    capacityHindi: '10+ बॉक्स',
  },
];

export const LoaderTypeSelector: React.FC<LoaderTypeSelectorProps> = ({
  selectedType,
  onTypeSelect,
  style,
  disabled = false,
}) => {
  const getCardStyle = (type: LoaderType): ViewStyle => {
    const isSelected = selectedType === type;
    const typeColor = getLoaderTypeColor(type);
    
    return {
      ...styles.card,
      borderColor: isSelected ? colors.primary : colors.gray200,
      borderWidth: isSelected ? 2 : 1,
      backgroundColor: isSelected ? colors.primaryLight : colors.white,
      opacity: disabled ? 0.6 : 1,
    };
  };

  const getIconStyle = (type: LoaderType): TextStyle => {
    const isSelected = selectedType === type;
    
    return {
      fontSize: 32,
      marginBottom: spacing.sm,
      opacity: isSelected ? 1 : 0.7,
    };
  };

  const getLabelStyle = (type: LoaderType): TextStyle => {
    const isSelected = selectedType === type;
    
    return {
      ...typography.h4,
      color: isSelected ? colors.primary : colors.text,
      textAlign: 'center',
      marginBottom: spacing.xs,
    };
  };

  const getDescriptionStyle = (type: LoaderType): TextStyle => {
    const isSelected = selectedType === type;
    
    return {
      ...typography.captionSmall,
      color: isSelected ? colors.primary : colors.textSecondary,
      textAlign: 'center',
    };
  };

  const renderLoaderCard = (option: LoaderTypeOption) => (
    <TouchableOpacity
      key={option.type}
      style={getCardStyle(option.type)}
      onPress={() => onTypeSelect(option.type)}
      disabled={disabled}
      activeOpacity={0.8}
    >
      <Text style={getIconStyle(option.type)}>{option.icon}</Text>
      <Text style={getLabelStyle(option.type)}>{option.label}</Text>
      <Text style={getDescriptionStyle(option.type)}>{option.description}</Text>
      <Text style={getDescriptionStyle(option.type)}>{option.capacity}</Text>
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, style]}>
      <Text style={styles.title}>Select Loader Type</Text>
      <View style={styles.optionsContainer}>
        {loaderTypes.map(renderLoaderCard)}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: spacing.base,
  },
  title: {
    ...typography.h4,
    color: colors.text,
    marginBottom: spacing.md,
  },
  optionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: spacing.sm,
  },
  card: {
    flex: 1,
    alignItems: 'center',
    padding: spacing.base,
    borderRadius: spacing.borderRadius.md,
    minHeight: 120,
    justifyContent: 'center',
  },
});

export default LoaderTypeSelector;
