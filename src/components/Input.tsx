import React, { useState, forwardRef } from 'react';
import {
  TextInput,
  View,
  Text,
  StyleSheet,
  TextInputProps,
  ViewStyle,
  TextStyle,
  TouchableOpacity,
} from 'react-native';
import { colors, typography, spacing, componentStyles } from '@/theme';

interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
  hint?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  onRightIconPress?: () => void;
  containerStyle?: ViewStyle;
  inputStyle?: TextStyle;
  labelStyle?: TextStyle;
  errorStyle?: TextStyle;
  hintStyle?: TextStyle;
  required?: boolean;
  disabled?: boolean;
}

export const Input = forwardRef<TextInput, InputProps>(({
  label,
  error,
  hint,
  leftIcon,
  rightIcon,
  onRightIconPress,
  containerStyle,
  inputStyle,
  labelStyle,
  errorStyle,
  hintStyle,
  required = false,
  disabled = false,
  style,
  ...props
}, ref) => {
  const [isFocused, setIsFocused] = useState(false);

  const getInputContainerStyle = (): ViewStyle => {
    const baseStyle = componentStyles.input.default;
    let dynamicStyle = {};

    if (isFocused) {
      dynamicStyle = componentStyles.input.focused;
    }

    if (error) {
      dynamicStyle = { ...dynamicStyle, ...componentStyles.input.error };
    }

    if (disabled) {
      dynamicStyle = { ...dynamicStyle, backgroundColor: colors.gray100, opacity: 0.6 };
    }

    return {
      ...baseStyle,
      ...dynamicStyle,
      ...style,
    };
  };

  const getLabelStyle = (): TextStyle => {
    return {
      ...typography.inputLabel,
      color: error ? colors.error : colors.text,
      marginBottom: spacing.xs,
      ...labelStyle,
    };
  };

  const getInputStyle = (): TextStyle => {
    return {
      ...typography.input,
      color: colors.text,
      flex: 1,
      paddingVertical: 0, // Remove default padding
      ...inputStyle,
    };
  };

  const getErrorStyle = (): TextStyle => {
    return {
      ...typography.captionSmall,
      color: colors.error,
      marginTop: spacing.xs,
      ...errorStyle,
    };
  };

  const getHintStyle = (): TextStyle => {
    return {
      ...typography.captionSmall,
      color: colors.textSecondary,
      marginTop: spacing.xs,
      ...hintStyle,
    };
  };

  const handleFocus = (e: any) => {
    setIsFocused(true);
    props.onFocus?.(e);
  };

  const handleBlur = (e: any) => {
    setIsFocused(false);
    props.onBlur?.(e);
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {label && (
        <Text style={getLabelStyle()}>
          {label}
          {required && <Text style={styles.required}> *</Text>}
        </Text>
      )}
      
      <View style={getInputContainerStyle()}>
        {leftIcon && (
          <View style={styles.iconContainer}>
            {leftIcon}
          </View>
        )}
        
        <TextInput
          ref={ref}
          style={getInputStyle()}
          placeholderTextColor={colors.textSecondary}
          onFocus={handleFocus}
          onBlur={handleBlur}
          editable={!disabled}
          {...props}
        />
        
        {rightIcon && (
          <TouchableOpacity
            style={styles.iconContainer}
            onPress={onRightIconPress}
            disabled={!onRightIconPress}
          >
            {rightIcon}
          </TouchableOpacity>
        )}
      </View>
      
      {error && <Text style={getErrorStyle()}>{error}</Text>}
      {hint && !error && <Text style={getHintStyle()}>{hint}</Text>}
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    marginBottom: spacing.base,
  },
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.sm,
  },
  required: {
    color: colors.error,
  },
});

Input.displayName = 'Input';

export default Input;
