import React from 'react';
import {
  View,
  StyleSheet,
  ViewStyle,
  TouchableOpacity,
  TouchableOpacityProps,
} from 'react-native';
import { colors, spacing, componentStyles } from '@/theme';

interface CardProps extends TouchableOpacityProps {
  children: React.ReactNode;
  variant?: 'default' | 'elevated';
  padding?: number;
  margin?: number;
  style?: ViewStyle;
  touchable?: boolean;
}

export const Card: React.FC<CardProps> = ({
  children,
  variant = 'default',
  padding,
  margin,
  style,
  touchable = false,
  ...props
}) => {
  const getCardStyle = (): ViewStyle => {
    const baseStyle = componentStyles.card[variant];
    
    return {
      ...baseStyle,
      ...(padding !== undefined && { padding }),
      ...(margin !== undefined && { margin }),
      ...style,
    };
  };

  if (touchable) {
    return (
      <TouchableOpacity
        style={getCardStyle()}
        activeOpacity={0.8}
        {...props}
      >
        {children}
      </TouchableOpacity>
    );
  }

  return (
    <View style={getCardStyle()}>
      {children}
    </View>
  );
};

export default Card;
