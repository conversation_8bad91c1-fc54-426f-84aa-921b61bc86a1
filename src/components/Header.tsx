import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  TextStyle,
  StatusBar,
  Platform,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { colors, typography, spacing } from '@/theme';

interface HeaderProps {
  title: string;
  subtitle?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  onLeftPress?: () => void;
  onRightPress?: () => void;
  backgroundColor?: string;
  titleColor?: string;
  style?: ViewStyle;
  titleStyle?: TextStyle;
  showBackButton?: boolean;
  transparent?: boolean;
}

export const Header: React.FC<HeaderProps> = ({
  title,
  subtitle,
  leftIcon,
  rightIcon,
  onLeftPress,
  onRightPress,
  backgroundColor = colors.white,
  titleColor = colors.text,
  style,
  titleStyle,
  showBackButton = false,
  transparent = false,
}) => {
  const insets = useSafeAreaInsets();

  const getHeaderStyle = (): ViewStyle => ({
    backgroundColor: transparent ? 'transparent' : backgroundColor,
    paddingTop: insets.top,
    paddingHorizontal: spacing.base,
    paddingBottom: spacing.md,
    borderBottomWidth: transparent ? 0 : 1,
    borderBottomColor: colors.gray200,
    ...style,
  });

  const getTitleStyle = (): TextStyle => ({
    ...typography.h3,
    color: titleColor,
    textAlign: 'center',
    flex: 1,
    ...titleStyle,
  });

  const getSubtitleStyle = (): TextStyle => ({
    ...typography.caption,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: spacing.xs,
  });

  const renderBackButton = () => {
    if (!showBackButton && !leftIcon) return null;

    return (
      <TouchableOpacity
        style={styles.iconButton}
        onPress={onLeftPress}
        activeOpacity={0.7}
      >
        {leftIcon || (
          <Text style={styles.backIcon}>←</Text>
        )}
      </TouchableOpacity>
    );
  };

  const renderRightButton = () => {
    if (!rightIcon) return <View style={styles.iconButton} />;

    return (
      <TouchableOpacity
        style={styles.iconButton}
        onPress={onRightPress}
        activeOpacity={0.7}
      >
        {rightIcon}
      </TouchableOpacity>
    );
  };

  return (
    <>
      <StatusBar
        backgroundColor={transparent ? 'transparent' : backgroundColor}
        barStyle={backgroundColor === colors.white ? 'dark-content' : 'light-content'}
        translucent={transparent}
      />
      <View style={getHeaderStyle()}>
        <View style={styles.headerContent}>
          {renderBackButton()}
          <View style={styles.titleContainer}>
            <Text style={getTitleStyle()}>{title}</Text>
            {subtitle && <Text style={getSubtitleStyle()}>{subtitle}</Text>}
          </View>
          {renderRightButton()}
        </View>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    minHeight: 44,
  },
  titleContainer: {
    flex: 1,
    alignItems: 'center',
  },
  iconButton: {
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backIcon: {
    fontSize: 24,
    color: colors.text,
    fontWeight: '600',
  },
});

export default Header;
