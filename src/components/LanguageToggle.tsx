import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { colors, typography, spacing } from '@/theme';

interface LanguageToggleProps {
  currentLanguage: 'en' | 'hi';
  onLanguageChange: (language: 'en' | 'hi') => void;
  style?: ViewStyle;
}

export const LanguageToggle: React.FC<LanguageToggleProps> = ({
  currentLanguage,
  onLanguageChange,
  style,
}) => {
  const getButtonStyle = (isActive: boolean): ViewStyle => ({
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: spacing.borderRadius.base,
    backgroundColor: isActive ? colors.primary : 'transparent',
    minWidth: 40,
    alignItems: 'center',
  });

  const getTextStyle = (isActive: boolean): TextStyle => ({
    ...typography.buttonSmall,
    color: isActive ? colors.white : colors.textSecondary,
    fontWeight: isActive ? '700' : '500',
  });

  return (
    <View style={[styles.container, style]}>
      <TouchableOpacity
        style={getButtonStyle(currentLanguage === 'en')}
        onPress={() => onLanguageChange('en')}
        activeOpacity={0.8}
      >
        <Text style={getTextStyle(currentLanguage === 'en')}>EN</Text>
      </TouchableOpacity>
      
      <View style={styles.separator} />
      
      <TouchableOpacity
        style={getButtonStyle(currentLanguage === 'hi')}
        onPress={() => onLanguageChange('hi')}
        activeOpacity={0.8}
      >
        <Text style={getTextStyle(currentLanguage === 'hi')}>हिं</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: colors.gray100,
    borderRadius: spacing.borderRadius.base,
    padding: 2,
    alignItems: 'center',
  },
  separator: {
    width: 1,
    height: 20,
    backgroundColor: colors.gray300,
    marginHorizontal: 2,
  },
});

export default LanguageToggle;
