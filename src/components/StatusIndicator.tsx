import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { colors, typography, spacing, getStatusColor } from '@/theme';
import { BookingStatus } from '@/types';

interface StatusIndicatorProps {
  status: BookingStatus | 'online' | 'offline';
  label?: string;
  size?: 'small' | 'medium' | 'large';
  showDot?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

const statusLabels: Record<string, { en: string; hi: string }> = {
  pending: { en: 'Pending', hi: 'प्रतीक्षित' },
  confirmed: { en: 'Confirmed', hi: 'पुष्ट' },
  driver_assigned: { en: 'Driver Assigned', hi: 'ड्राइवर नियुक्त' },
  pickup_arrived: { en: 'Driver Arrived', hi: 'ड्राइवर पहुंचा' },
  pickup_completed: { en: 'Pickup Done', hi: 'पिकअप पूरा' },
  in_transit: { en: 'In Transit', hi: 'रास्ते में' },
  delivered: { en: 'Delivered', hi: 'डिलीवर' },
  cancelled: { en: 'Cancelled', hi: 'रद्द' },
  payment_pending: { en: 'Payment Pending', hi: 'भुगतान प्रतीक्षित' },
  completed: { en: 'Completed', hi: 'पूर्ण' },
  online: { en: 'Online', hi: 'ऑनलाइन' },
  offline: { en: 'Offline', hi: 'ऑफलाइन' },
};

export const StatusIndicator: React.FC<StatusIndicatorProps> = ({
  status,
  label,
  size = 'medium',
  showDot = true,
  style,
  textStyle,
}) => {
  const statusColor = getStatusColor(status);
  const displayLabel = label || statusLabels[status]?.en || status;

  const getDotSize = (): number => {
    switch (size) {
      case 'small':
        return 8;
      case 'large':
        return 16;
      default:
        return 12;
    }
  };

  const getTextStyle = (): TextStyle => {
    let baseStyle;
    
    switch (size) {
      case 'small':
        baseStyle = typography.captionSmall;
        break;
      case 'large':
        baseStyle = typography.body;
        break;
      default:
        baseStyle = typography.caption;
    }

    return {
      ...baseStyle,
      color: statusColor,
      fontWeight: '600',
      ...textStyle,
    };
  };

  const getDotStyle = (): ViewStyle => ({
    width: getDotSize(),
    height: getDotSize(),
    borderRadius: getDotSize() / 2,
    backgroundColor: statusColor,
    marginRight: showDot ? spacing.xs : 0,
  });

  return (
    <View style={[styles.container, style]}>
      {showDot && <View style={getDotStyle()} />}
      <Text style={getTextStyle()}>{displayLabel}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});

export default StatusIndicator;
