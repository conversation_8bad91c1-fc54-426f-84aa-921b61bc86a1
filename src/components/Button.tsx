import React from 'react';
import {
  TouchableOpacity,
  Text,
  View,
  StyleSheet,
  ViewStyle,
  TextStyle,
  ActivityIndicator,
  TouchableOpacityProps,
} from 'react-native';
import { colors, typography, spacing, componentStyles } from '@/theme';

interface ButtonProps extends TouchableOpacityProps {
  title: string;
  variant?: 'primary' | 'secondary' | 'text';
  size?: 'small' | 'medium' | 'large';
  loading?: boolean;
  disabled?: boolean;
  fullWidth?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  style?: ViewStyle;
  textStyle?: TextStyle;
}

export const Button: React.FC<ButtonProps> = ({
  title,
  variant = 'primary',
  size = 'medium',
  loading = false,
  disabled = false,
  fullWidth = true,
  icon,
  iconPosition = 'left',
  style,
  textStyle,
  onPress,
  ...props
}) => {
  const getButtonStyle = (): ViewStyle => {
    const baseStyle = componentStyles.button[variant];
    const sizeStyle = getSizeStyle();
    
    return {
      ...baseStyle,
      ...sizeStyle,
      ...(fullWidth && { width: '100%' }),
      ...(disabled && { opacity: 0.6 }),
      ...style,
    };
  };

  const getSizeStyle = (): ViewStyle => {
    switch (size) {
      case 'small':
        return {
          height: 40,
          paddingHorizontal: spacing.md,
        };
      case 'large':
        return {
          height: 64,
          paddingHorizontal: spacing.xl,
        };
      default:
        return {
          height: spacing.component.button,
          paddingHorizontal: spacing.buttonPadding,
        };
    }
  };

  const getTextStyle = (): TextStyle => {
    const baseTextStyle = getBaseTextStyle();
    const sizeTextStyle = getSizeTextStyle();
    
    return {
      ...baseTextStyle,
      ...sizeTextStyle,
      ...textStyle,
    };
  };

  const getBaseTextStyle = (): TextStyle => {
    switch (variant) {
      case 'primary':
        return {
          ...typography.button,
          color: colors.white,
        };
      case 'secondary':
        return {
          ...typography.button,
          color: colors.secondary,
        };
      case 'text':
        return {
          ...typography.button,
          color: colors.secondary,
        };
      default:
        return typography.button;
    }
  };

  const getSizeTextStyle = (): TextStyle => {
    switch (size) {
      case 'small':
        return {
          fontSize: 14,
          lineHeight: 18,
        };
      case 'large':
        return {
          fontSize: 18,
          lineHeight: 24,
        };
      default:
        return {};
    }
  };

  const renderContent = () => {
    if (loading) {
      return (
        <ActivityIndicator
          size="small"
          color={variant === 'primary' ? colors.white : colors.secondary}
        />
      );
    }

    if (icon) {
      return (
        <View style={styles.contentContainer}>
          {iconPosition === 'left' && icon}
          <Text style={getTextStyle()}>{title}</Text>
          {iconPosition === 'right' && icon}
        </View>
      );
    }

    return <Text style={getTextStyle()}>{title}</Text>;
  };

  return (
    <TouchableOpacity
      style={getButtonStyle()}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.8}
      {...props}
    >
      {renderContent()}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: spacing.sm,
  },
});

export default Button;
