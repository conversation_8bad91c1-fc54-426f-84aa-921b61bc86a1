import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Text } from 'react-native';
import { colors, typography } from '@/theme';
import { CustomerStackParamList } from '@/types';

// Import Customer Screens
import SplashScreen from '@/screens/customer/SplashScreen';
import LoginScreen from '@/screens/customer/LoginScreen';
import DashboardScreen from '@/screens/customer/DashboardScreen';

const Stack = createNativeStackNavigator();
const Tab = createBottomTabNavigator<CustomerStackParamList>();

// Tab Navigator for authenticated customer screens
const CustomerTabNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={{
        headerShown: false,
        tabBarStyle: {
          backgroundColor: colors.white,
          borderTopColor: colors.gray200,
          height: 80,
          paddingBottom: 8,
          paddingTop: 8,
        },
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.textSecondary,
        tabBarLabelStyle: {
          ...typography.captionSmall,
          fontWeight: '600',
        },
      }}
    >
      <Tab.Screen
        name="Dashboard"
        component={DashboardScreen}
        options={{
          tabBarLabel: 'Home',
          tabBarIcon: ({ color, focused }) => (
            <Text style={{ fontSize: 24, color }}>
              {focused ? '🏠' : '🏠'}
            </Text>
          ),
        }}
      />
      
      <Tab.Screen
        name="FareEstimate"
        component={DashboardScreen} // Placeholder
        options={{
          tabBarLabel: 'Bookings',
          tabBarIcon: ({ color, focused }) => (
            <Text style={{ fontSize: 24, color }}>
              {focused ? '📋' : '📋'}
            </Text>
          ),
        }}
      />
      
      <Tab.Screen
        name="Tracking"
        component={DashboardScreen} // Placeholder
        options={{
          tabBarLabel: 'Track',
          tabBarIcon: ({ color, focused }) => (
            <Text style={{ fontSize: 24, color }}>
              {focused ? '📍' : '📍'}
            </Text>
          ),
        }}
      />
      
      <Tab.Screen
        name="History"
        component={DashboardScreen} // Placeholder
        options={{
          tabBarLabel: 'History',
          tabBarIcon: ({ color, focused }) => (
            <Text style={{ fontSize: 24, color }}>
              {focused ? '📜' : '📜'}
            </Text>
          ),
        }}
      />
      
      <Tab.Screen
        name="Payment"
        component={DashboardScreen} // Placeholder
        options={{
          tabBarLabel: 'Profile',
          tabBarIcon: ({ color, focused }) => (
            <Text style={{ fontSize: 24, color }}>
              {focused ? '👤' : '👤'}
            </Text>
          ),
        }}
      />
    </Tab.Navigator>
  );
};

// Main Customer Navigator
export const CustomerNavigator = () => {
  const [isAuthenticated, setIsAuthenticated] = React.useState(false);
  const [showSplash, setShowSplash] = React.useState(true);

  const handleSplashComplete = () => {
    setShowSplash(false);
  };

  const handleLoginSuccess = () => {
    setIsAuthenticated(true);
  };

  const handleLogout = () => {
    setIsAuthenticated(false);
  };

  const handleBack = () => {
    setShowSplash(true);
  };

  if (showSplash) {
    return <SplashScreen onComplete={handleSplashComplete} />;
  }

  if (!isAuthenticated) {
    return (
      <LoginScreen
        onLoginSuccess={handleLoginSuccess}
        onBack={handleBack}
      />
    );
  }

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        animation: 'slide_from_right',
      }}
    >
      <Stack.Screen name="Main" component={CustomerTabNavigator} />
    </Stack.Navigator>
  );
};

export default CustomerNavigator;
