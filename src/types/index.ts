// Core Types
export interface User {
  id: string;
  phone: string;
  email?: string;
  name: string;
  languagePreference: 'en' | 'hi';
  userType: 'customer' | 'driver' | 'vendor';
  createdAt: string;
  updatedAt: string;
}

export interface Customer extends User {
  userType: 'customer';
  savedAddresses: Address[];
  paymentMethods: PaymentMethod[];
}

export interface Driver extends User {
  userType: 'driver';
  vehicleNumber: string;
  licenseNumber: string;
  isOnline: boolean;
  currentLocation: Location;
  rating: number;
  totalTrips: number;
  earnings: number;
  documents: DriverDocument[];
}

export interface Vendor extends User {
  userType: 'vendor';
  companyName: string;
  creditLimit: number;
  balance: number;
  billingAddress: Address;
  contactPerson: string;
  status: 'active' | 'inactive' | 'suspended';
}

// Location & Address Types
export interface Location {
  latitude: number;
  longitude: number;
}

export interface Address {
  id: string;
  label: string;
  address: string;
  location: Location;
  landmark?: string;
  type: 'home' | 'work' | 'other';
}

// Booking & Delivery Types
export interface Booking {
  id: string;
  customerId: string;
  driverId?: string;
  vendorId?: string;
  pickupAddress: Address;
  dropAddress: Address;
  loaderType: LoaderType;
  status: BookingStatus;
  fareAmount: number;
  otp: string;
  scheduledTime?: string;
  createdAt: string;
  updatedAt: string;
  estimatedDistance: number;
  estimatedDuration: number;
}

export type LoaderType = 'small' | 'medium' | 'large';

export type BookingStatus = 
  | 'pending'
  | 'confirmed'
  | 'driver_assigned'
  | 'pickup_arrived'
  | 'pickup_completed'
  | 'in_transit'
  | 'delivered'
  | 'cancelled'
  | 'payment_pending'
  | 'completed';

// Payment Types
export interface PaymentMethod {
  id: string;
  type: 'upi' | 'card' | 'wallet' | 'cash';
  details: string;
  isDefault: boolean;
}

export interface Payment {
  id: string;
  bookingId: string;
  amount: number;
  method: PaymentMethod;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  transactionId?: string;
  createdAt: string;
}

// Driver Specific Types
export interface DriverDocument {
  id: string;
  type: 'license' | 'vehicle_registration' | 'insurance' | 'pollution_certificate';
  url: string;
  status: 'pending' | 'verified' | 'rejected';
  expiryDate?: string;
}

export interface Earnings {
  totalEarnings: number;
  todayEarnings: number;
  weekEarnings: number;
  monthEarnings: number;
  tripsCompleted: number;
  hoursOnline: number;
  rating: number;
  fuelSavings: number;
}

// Vendor Specific Types
export interface VendorOrder {
  id: string;
  vendorId: string;
  orderReference: string;
  pickupAddress: Address;
  dropAddress: Address;
  loaderType: LoaderType;
  status: BookingStatus;
  amount: number;
  scheduledTime: string;
  driverId?: string;
  createdAt: string;
}

export interface Invoice {
  id: string;
  vendorId: string;
  orderIds: string[];
  totalAmount: number;
  paidAmount: number;
  dueAmount: number;
  dueDate: string;
  status: 'pending' | 'paid' | 'overdue';
  createdAt: string;
}

// Navigation Types
export type RootStackParamList = {
  Splash: undefined;
  Auth: undefined;
  Main: undefined;
};

export type CustomerStackParamList = {
  Dashboard: undefined;
  FareEstimate: {
    pickupAddress: Address;
    dropAddress: Address;
    loaderType: LoaderType;
  };
  Tracking: {
    bookingId: string;
  };
  Payment: {
    bookingId: string;
  };
  History: undefined;
};

export type DriverStackParamList = {
  Dashboard: undefined;
  Navigation: {
    bookingId: string;
  };
  DeliveryCompletion: {
    bookingId: string;
  };
  Earnings: undefined;
};

export type VendorStackParamList = {
  Dashboard: undefined;
  ScheduleDelivery: undefined;
  TrackOrder: {
    orderId: string;
  };
  Billing: undefined;
};

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

// Form Types
export interface LoginForm {
  phone: string;
  otp?: string;
  email?: string;
  password?: string;
}

export interface BookingForm {
  pickupAddress: Address;
  dropAddress: Address;
  loaderType: LoaderType;
  scheduledTime?: string;
  notes?: string;
}

export interface ScheduleDeliveryForm {
  orderReference: string;
  pickupAddress: Address;
  dropAddress: Address;
  loaderType: LoaderType;
  scheduledTime: string;
  isRecurring: boolean;
  recurringPattern?: 'daily' | 'weekly' | 'monthly';
}

// App Configuration
export interface AppConfig {
  apiBaseUrl: string;
  googleMapsApiKey: string;
  razorpayKey: string;
  firebaseConfig: object;
  socketUrl: string;
}

// Language Support
export interface LanguageStrings {
  [key: string]: string | LanguageStrings;
}

export interface I18nConfig {
  en: LanguageStrings;
  hi: LanguageStrings;
}
