{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "edit": "Edit", "delete": "Delete", "back": "Back", "next": "Next", "done": "Done", "retry": "Retry", "refresh": "Refresh", "search": "Search", "filter": "Filter", "sort": "Sort", "select": "Select", "submit": "Submit", "continue": "Continue", "skip": "<PERSON><PERSON>", "close": "Close", "open": "Open", "view": "View", "download": "Download", "upload": "Upload", "share": "Share", "copy": "Copy", "paste": "Paste", "cut": "Cut", "undo": "Undo", "redo": "Redo"}, "auth": {"login": "<PERSON><PERSON>", "signup": "Sign Up", "logout": "Logout", "welcomeBack": "Welcome Back!", "loginToBook": "Login to book your loader", "phoneNumber": "Phone Number", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "forgotPassword": "Forgot Password?", "getOtp": "Get OTP", "verifyOtp": "Verify OTP", "enterOtp": "Enter OTP", "resendOtp": "Resend OTP", "continueWithGoogle": "Continue with Google", "continueWithEmail": "Continue with <PERSON>ail", "newUser": "New user?", "signUpHere": "Sign up here", "alreadyHaveAccount": "Already have an account?", "loginHere": "Login here", "termsAndConditions": "Terms and Conditions", "privacyPolicy": "Privacy Policy", "agreeToTerms": "I agree to the Terms and Conditions"}, "customer": {"appName": "EV Loader", "tagline": "Sustainable • Fast • Safe", "connectingMessage": "Connecting you to clean delivery solutions...", "hiUser": "Hi {{name}}! 👋", "whereNeedLoader": "Where do you need a loader?", "pickupLocation": "Pickup Location", "dropLocation": "Drop Location", "currentLocation": "Current Location", "whereTo": "Where to?", "selectLoaderType": "Select Loader Type", "bookNow": "Book Now", "recentBookings": "Recent Bookings", "quickBook": "Quick Book", "fareDetails": "Fare Details", "fareBreakdown": "Fare Breakdown", "baseFare": "Base Fare", "distanceCharge": "Distance Charge", "timeCharge": "Time Charge", "subtotal": "Subtotal", "gst": "GST (18%)", "totalAmount": "Total Amount", "evDelivery": "EV Delivery - Zero Emission", "confirmBooking": "Confirm Booking", "liveTracking": "Live Tracking", "driverArriving": "Driver is {{time}} away", "arrivingAtPickup": "Arriving at pickup", "callDriver": "Call", "chatDriver": "Cha<PERSON>", "shareLocation": "Share Live Location", "deliveryCompleted": "Delivery Completed! 🎉", "tripSummary": "<PERSON> Summary", "rateExperience": "Rate your experience:", "paymentMethod": "Payment Method", "payAmount": "Pay ₹{{amount}}", "addTip": "Add Tip", "downloadReceipt": "Download Receipt", "bookingHistory": "Booking History", "rebook": "Rebook", "receipt": "Receipt", "support": "Support", "loadMoreTrips": "Load More Trips"}, "driver": {"driverPartner": "Driver Partner", "startEarning": "Start earning with us!", "driverId": "Driver ID/Phone", "goodMorning": "Good Morning, {{name}}! 🌅", "status": "Status", "online": "Online", "offline": "Offline", "goOffline": "Go Offline", "emergency": "Emergency", "todaySummary": "Today's Summary", "trips": "Trips", "earnings": "Earnings", "rating": "Rating", "hours": "Hours", "activeDeliveries": "Active Deliveries", "urgent": "URGENT", "scheduled": "Scheduled", "pickupIn": "Pickup in {{time}}", "viewDetails": "View Details", "navigate": "Navigate", "newRequests": "New Requests", "navigation": "Navigation", "goingToPickup": "Going to Pickup", "customer": "Customer", "callCustomer": "Call Customer", "chat": "Cha<PERSON>", "tripValue": "Trip Value", "arrivedAtPickup": "Arrived at Pickup", "voiceNavigation": "Voice Navigation", "reportIssue": "Report Issue", "completeDelivery": "Complete Delivery", "atDropLocation": "At Drop Location", "getOtpFromCustomer": "Get OTP from Customer:", "enterOtp": "Enter 4-digit OTP", "issuesWithDelivery": "Issues with delivery?", "customerNotAvailable": "Customer not available", "wrongAddress": "Wrong address", "loadDamaged": "Load damaged", "otherIssue": "Other issue", "totalEarnings": "Total Earnings", "thisMonth": "This Month", "todaysBreakdown": "Today's Breakdown", "tripsCompleted": "Trips Completed", "totalDistance": "Total Distance", "onlineHours": "Online Hours", "grossEarnings": "Gross Earnings", "commission": "Commission (15%)", "netEarnings": "Net Earnings", "fuelSavings": "Fuel Savings 🌱", "performance": "Performance", "acceptanceRate": "Acceptance Rate", "completionRate": "Completion Rate", "onTimeRate": "On-time Rate", "withdrawEarnings": "Withdraw Earnings", "paymentHistory": "Payment History", "taxReport": "Tax Report"}, "vendor": {"vendorPortal": "Vendor Portal", "manageDeliveries": "Manage your deliveries", "companyId": "Company ID", "username": "Username", "newVendor": "New vendor?", "registerHere": "Register Here", "contactSales": "Contact Sales", "welcomeCompany": "Welcome, {{company}}! 👋", "quickStats": "Quick Stats", "active": "Active", "pending": "Pending", "completed": "Completed", "monthly": "Monthly", "scheduleDelivery": "Schedule Delivery", "todaysDeliveries": "Today's Deliveries", "inProgress": "In Progress", "orderNumber": "Order #{{number}}", "pickup": "Pickup", "drop": "Drop", "driver": "Driver", "trackLive": "Track Live", "details": "Details", "modify": "Modify", "viewAll": "View All", "export": "Export", "deliveryDetails": "Delivery Details", "orderReference": "Order Reference", "pickupAddress": "Pickup Address", "deliveryAddress": "Delivery Address", "useSavedLocation": "Use Saved Location", "schedule": "Schedule", "now": "Now", "later": "Later", "recurring": "Recurring", "getQuoteSchedule": "Get Quote & Schedule", "liveTracking": "Live Tracking", "inTransit": "In Transit", "eta": "ETA", "vehicle": "Vehicle", "tripProgress": "Trip Progress", "orderConfirmed": "Order Confirmed", "driverAssigned": "Driver Assigned", "pickupCompleted": "Pickup Completed", "deliveryPending": "Delivery Pending", "loadDetails": "Load Details", "weight": "Weight", "shareTracking": "Share Tracking", "deliveryNotes": "Delivery Notes", "paymentBilling": "Payment & Billing", "accountSummary": "Account Summary", "currentBalance": "Current Balance", "creditLimit": "Credit Limit", "nextPaymentDue": "Next Payment Due", "totalDeliveries": "Total Deliveries", "totalAmount": "Total Amount", "paid": "Paid", "outstanding": "Outstanding", "recentTransactions": "Recent Transactions", "invoice": "Invoice", "makePayment": "Make Payment", "downloadStatement": "Download Statement", "taxInfo": "Tax Info"}, "loaderTypes": {"small": "Small", "medium": "Medium", "large": "Large", "smallDesc": "Up to 500kg", "mediumDesc": "Up to 1000kg", "largeDesc": "Up to 2000kg", "smallCapacity": "2-3 boxes", "mediumCapacity": "5-8 boxes", "largeCapacity": "10+ boxes"}, "status": {"pending": "Pending", "confirmed": "Confirmed", "driverAssigned": "Driver Assigned", "pickupArrived": "Driver Arrived", "pickupCompleted": "Pickup <PERSON>", "inTransit": "In Transit", "delivered": "Delivered", "cancelled": "Cancelled", "paymentPending": "Payment Pending", "completed": "Completed", "online": "Online", "offline": "Offline"}, "navigation": {"home": "Home", "trips": "Trips", "earn": "<PERSON><PERSON><PERSON>", "stats": "Stats", "profile": "Profile", "orders": "Orders", "track": "Track", "billing": "Billing", "settings": "Settings"}, "errors": {"networkError": "Network error. Please check your connection.", "serverError": "Server error. Please try again later.", "invalidCredentials": "Invalid credentials. Please try again.", "otpExpired": "OTP has expired. Please request a new one.", "invalidOtp": "Invalid OTP. Please try again.", "locationPermissionDenied": "Location permission denied.", "cameraPermissionDenied": "Camera permission denied.", "storagePermissionDenied": "Storage permission denied.", "somethingWentWrong": "Something went wrong. Please try again."}}