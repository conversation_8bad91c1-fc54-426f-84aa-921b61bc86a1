# Vendor App Wireframes & User Flow

## App Flow: Login → Schedule Delivery → Live Track → Payment Log

---

## Screen 1: Vendor Login
```
┌─────────────────────────────────┐
│         [EN | हिं]               │
│                                 │
│         🚛⚡ EV LOADER           │
│       VENDOR PORTAL             │
│                                 │
│    Manage your deliveries       │
│                                 │
│  ┌─────────────────────────────┐ │
│  │ 🏢 Company ID               │ │
│  │ |_________________________| │ │
│  └─────────────────────────────┘ │
│                                 │
│  ┌─────────────────────────────┐ │
│  │ 👤 Username                 │ │
│  │ |_________________________| │ │
│  └─────────────────────────────┘ │
│                                 │
│  ┌─────────────────────────────┐ │
│  │ 🔒 Password                 │ │
│  │ |_________________________| │ │
│  └─────────────────────────────┘ │
│                                 │
│  ┌─────────────────────────────┐ │
│  │        [LOGIN]              │ │
│  └─────────────────────────────┘ │
│                                 │
│    Forgot Password?             │
│                                 │
│  New vendor? [Register Here]    │
│   Contact Sales: +91-XXXXXXXXX  │
└─────────────────────────────────┘
```
**Features**: Company-based login, vendor registration, sales contact

---

## Screen 2: Schedule Delivery Dashboard
```
┌─────────────────────────────────┐
│  ☰ [Menu]  Dashboard  🔔 [Bell] │
│                                 │
│  Welcome, TechCorp Logistics! 👋│
│                                 │
│  📊 Quick Stats:                │
│  ┌─────────────────────────────┐ │
│  │ Active: 12 | Pending: 5     │ │
│  │ Completed: 156 | Monthly    │ │
│  └─────────────────────────────┘ │
│                                 │
│  ┌─────────────────────────────┐ │
│  │    [+ SCHEDULE DELIVERY]    │ │
│  └─────────────────────────────┘ │
│                                 │
│  🚛 Today's Deliveries:         │
│                                 │
│  ┌─────────────────────────────┐ │
│  │ 🟢 In Progress              │ │
│  │ Order #*********            │ │
│  │ Pickup: Warehouse A         │ │
│  │ Drop: Client Site B         │ │
│  │ Driver: Rajesh K. ⭐4.8     │ │
│  │ [TRACK LIVE] [DETAILS]      │ │
│  └─────────────────────────────┘ │
│                                 │
│  ┌─────────────────────────────┐ │
│  │ 🟡 Scheduled - 2:30 PM      │ │
│  │ Order #*********            │ │
│  │ Pickup: Factory C           │ │
│  │ Drop: Distribution Hub      │ │
│  │ [MODIFY] [CANCEL]           │ │
│  └─────────────────────────────┘ │
│                                 │
│  [View All] [Filter] [Export]   │
└─────────────────────────────────┘
```
**Features**: Delivery overview, quick scheduling, order management, driver tracking

---

## Screen 2.1: Schedule New Delivery (Modal/New Screen)
```
┌─────────────────────────────────┐
│  ← [Back]   Schedule Delivery   │
│                                 │
│  📦 Delivery Details:           │
│                                 │
│  ┌─────────────────────────────┐ │
│  │ 📋 Order Reference          │ │
│  │ |_________________________| │ │
│  └─────────────────────────────┘ │
│                                 │
│  ┌─────────────────────────────┐ │
│  │ 📍 Pickup Address           │ │
│  │ |_________________________| │ │
│  │ [📍 Use Saved Location]     │ │
│  └─────────────────────────────┘ │
│                                 │
│  ┌─────────────────────────────┐ │
│  │ 📍 Delivery Address         │ │
│  │ |_________________________| │ │
│  │ [📍 Use Saved Location]     │ │
│  └─────────────────────────────┘ │
│                                 │
│  Loader Type:                   │
│  ┌─────┐ ┌─────┐ ┌─────┐        │
│  │ 🚛  │ │ 🚚  │ │ 🛻  │        │
│  │Small│ │Med. │ │Large│        │
│  └─────┘ └─────┘ └─────┘        │
│                                 │
│  ⏰ Schedule:                   │
│  [Now] [Later] [Recurring]      │
│                                 │
│  ┌─────────────────────────────┐ │
│  │   [GET QUOTE & SCHEDULE]    │ │
│  └─────────────────────────────┘ │
└─────────────────────────────────┘
```

---

## Screen 3: Live Track Delivery
```
┌─────────────────────────────────┐
│  ← [Back]    Live Tracking      │
│                                 │
│  Order #*********               │
│  🟢 In Transit                  │
│                                 │
│  ┌─────────────────────────────┐ │
│  │        [MAP VIEW]           │ │
│  │    🚛 ← Driver Location     │ │
│  │    📍 ← Pickup Done         │ │
│  │    📍 ← Drop Location       │ │
│  │    ┈┈┈ Route Path           │ │
│  │    ETA: 25 mins             │ │
│  └─────────────────────────────┘ │
│                                 │
│  👤 Driver: Rajesh Kumar ⭐4.8  │
│  📱 +91 98765 43210             │
│  🚛 Vehicle: DL-01-AB-1234      │
│                                 │
│  📊 Trip Progress:              │
│  ┌─────────────────────────────┐ │
│  │ ✅ Order Confirmed          │ │
│  │ ✅ Driver Assigned          │ │
│  │ ✅ Pickup Completed         │ │
│  │ 🔄 In Transit              │ │
│  │ ⏳ Delivery Pending         │ │
│  └─────────────────────────────┘ │
│                                 │
│  📦 Load Details:               │
│  Medium Load • 15 boxes         │
│  Weight: ~250 kg                │
│                                 │
│  [Call Driver] [Share Tracking] │
│  [Report Issue] [Delivery Notes]│
└─────────────────────────────────┘
```
**Features**: Real-time GPS tracking, driver contact, progress updates, issue reporting

---

## Screen 4: Payment Log & Billing
```
┌─────────────────────────────────┐
│  ← [Back]    Payment & Billing  │
│                                 │
│  💰 Account Summary             │
│  ┌─────────────────────────────┐ │
│  │ Current Balance: ₹2,450     │ │
│  │ Credit Limit: ₹50,000       │ │
│  │ Next Payment Due: Dec 25    │ │
│  └─────────────────────────────┘ │
│                                 │
│  [This Month] [Last Month] [All]│
│                                 │
│  📊 December 2024:              │
│  ┌─────────────────────────────┐ │
│  │ Total Deliveries: 45        │ │
│  │ Total Amount: ₹18,750       │ │
│  │ Paid: ₹16,300              │ │
│  │ Outstanding: ₹2,450         │ │
│  └─────────────────────────────┘ │
│                                 │
│  💳 Recent Transactions:        │
│                                 │
│  ┌─────────────────────────────┐ │
│  │ Dec 16 • Order #*********   │ │
│  │ Warehouse A → Client Site   │ │
│  │ ₹560 • Paid ✅             │ │
│  │ [Invoice] [Details]         │ │
│  └─────────────────────────────┘ │
│                                 │
│  ┌─────────────────────────────┐ │
│  │ Dec 15 • Order #EV2024000   │ │
│  │ Factory → Distribution Hub  │ │
│  │ ₹890 • Pending ⏳          │ │
│  │ [Invoice] [Details]         │ │
│  └─────────────────────────────┘ │
│                                 │
│  ┌─────────────────────────────┐ │
│  │      [MAKE PAYMENT]         │ │
│  └─────────────────────────────┘ │
│                                 │
│  [Download Statement] [Tax Info]│
└─────────────────────────────────┘
```
**Features**: Account balance, payment history, invoice management, payment processing

---

## Bottom Navigation (All Screens)
```
┌─────────────────────────────────┐
│ [🏠 Home] [📋 Orders] [📊 Track]│
│           [💰 Billing] [⚙️ Settings] │
└─────────────────────────────────┘
```

## Key Vendor App Features:
- **Bulk delivery scheduling**
- **Real-time order tracking**
- **Driver assignment and monitoring**
- **Comprehensive billing system**
- **Invoice generation and management**
- **Credit limit and payment tracking**
- **Delivery analytics and reporting**
- **Saved location management**
- **Recurring delivery setup**
- **Multi-user account access**
- **API integration capabilities**
- **Custom reporting and exports**
