import express from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { body, validationResult } from 'express-validator';
import { PrismaClient } from '@prisma/client';
import { sendOTP, verifyOTP } from '../../utils/sms';
import { sendEmail } from '../../utils/email';
import { logger } from '../../utils/logger';
import { authenticateToken } from '../../middleware/auth';
import { generateOTP, generateTokens } from '../../utils/auth';

const router = express.Router();
const prisma = new PrismaClient();

// Register new user
router.post('/register', [
  body('email').isEmail().normalizeEmail(),
  body('phone').isMobilePhone('en-IN'),
  body('password').isLength({ min: 8 }).matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/),
  body('firstName').trim().isLength({ min: 2 }),
  body('lastName').trim().isLength({ min: 2 }),
  body('role').isIn(['CUSTOMER', 'DRIVER']),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array(),
      });
    }

    const { email, phone, password, firstName, lastName, role } = req.body;

    // Check if user already exists
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [
          { email },
          { phone },
        ],
      },
    });

    if (existingUser) {
      return res.status(409).json({
        success: false,
        message: 'User already exists with this email or phone',
      });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create user
    const user = await prisma.user.create({
      data: {
        email,
        phone,
        password: hashedPassword,
        firstName,
        lastName,
        role,
      },
    });

    // Create role-specific profile
    if (role === 'CUSTOMER') {
      await prisma.customer.create({
        data: {
          userId: user.id,
        },
      });
    } else if (role === 'DRIVER') {
      await prisma.driver.create({
        data: {
          userId: user.id,
          licenseNumber: '', // To be updated later
          licenseExpiry: new Date(),
          aadharNumber: '',
          panNumber: '',
          bankAccountNumber: '',
          ifscCode: '',
        },
      });
    }

    // Generate OTP for verification
    const otp = generateOTP();
    
    // Store OTP in Redis with 10-minute expiry
    await redis.setex(`otp:${phone}`, 600, otp);

    // Send OTP via SMS
    await sendOTP(phone, otp);

    // Send welcome email
    await sendEmail({
      to: email,
      subject: 'Welcome to EV Loader Platform',
      template: 'welcome',
      data: {
        firstName,
        otp,
      },
    });

    logger.info(`User registered successfully: ${user.id}`, {
      userId: user.id,
      email,
      role,
    });

    res.status(201).json({
      success: true,
      message: 'User registered successfully. Please verify your phone number.',
      data: {
        userId: user.id,
        email: user.email,
        phone: user.phone,
        role: user.role,
        requiresVerification: true,
      },
    });

  } catch (error) {
    logger.error('Registration failed:', error);
    res.status(500).json({
      success: false,
      message: 'Registration failed',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error',
    });
  }
});

// Login user
router.post('/login', [
  body('identifier').notEmpty(), // email or phone
  body('password').notEmpty(),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array(),
      });
    }

    const { identifier, password } = req.body;

    // Find user by email or phone
    const user = await prisma.user.findFirst({
      where: {
        OR: [
          { email: identifier },
          { phone: identifier },
        ],
      },
      include: {
        customerProfile: true,
        driverProfile: true,
        adminProfile: true,
      },
    });

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials',
      });
    }

    // Check password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials',
      });
    }

    // Check if user is active
    if (user.status !== 'ACTIVE') {
      return res.status(403).json({
        success: false,
        message: 'Account is not active. Please contact support.',
      });
    }

    // Generate tokens
    const { accessToken, refreshToken } = generateTokens(user.id, user.role);

    // Update last login
    await prisma.user.update({
      where: { id: user.id },
      data: { lastLoginAt: new Date() },
    });

    // Store refresh token in Redis
    await redis.setex(`refresh_token:${user.id}`, 7 * 24 * 60 * 60, refreshToken);

    logger.info(`User logged in successfully: ${user.id}`, {
      userId: user.id,
      email: user.email,
      role: user.role,
    });

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user: {
          id: user.id,
          email: user.email,
          phone: user.phone,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
          profileImage: user.profileImage,
          emailVerified: user.emailVerified,
          phoneVerified: user.phoneVerified,
          profile: user.customerProfile || user.driverProfile || user.adminProfile,
        },
        tokens: {
          accessToken,
          refreshToken,
        },
      },
    });

  } catch (error) {
    logger.error('Login failed:', error);
    res.status(500).json({
      success: false,
      message: 'Login failed',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error',
    });
  }
});

// Verify OTP
router.post('/verify-otp', [
  body('phone').isMobilePhone('en-IN'),
  body('otp').isLength({ min: 6, max: 6 }),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array(),
      });
    }

    const { phone, otp } = req.body;

    // Get stored OTP from Redis
    const storedOTP = await redis.get(`otp:${phone}`);
    
    if (!storedOTP || storedOTP !== otp) {
      return res.status(400).json({
        success: false,
        message: 'Invalid or expired OTP',
      });
    }

    // Update user verification status
    const user = await prisma.user.update({
      where: { phone },
      data: { phoneVerified: true },
    });

    // Delete OTP from Redis
    await redis.del(`otp:${phone}`);

    logger.info(`Phone verified successfully: ${user.id}`, {
      userId: user.id,
      phone,
    });

    res.json({
      success: true,
      message: 'Phone number verified successfully',
      data: {
        userId: user.id,
        phoneVerified: true,
      },
    });

  } catch (error) {
    logger.error('OTP verification failed:', error);
    res.status(500).json({
      success: false,
      message: 'OTP verification failed',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error',
    });
  }
});

// Refresh token
router.post('/refresh-token', [
  body('refreshToken').notEmpty(),
], async (req, res) => {
  try {
    const { refreshToken } = req.body;

    // Verify refresh token
    const decoded = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET!) as any;
    
    // Check if token exists in Redis
    const storedToken = await redis.get(`refresh_token:${decoded.userId}`);
    if (!storedToken || storedToken !== refreshToken) {
      return res.status(401).json({
        success: false,
        message: 'Invalid refresh token',
      });
    }

    // Generate new tokens
    const { accessToken, refreshToken: newRefreshToken } = generateTokens(decoded.userId, decoded.role);

    // Update refresh token in Redis
    await redis.setex(`refresh_token:${decoded.userId}`, 7 * 24 * 60 * 60, newRefreshToken);

    res.json({
      success: true,
      message: 'Tokens refreshed successfully',
      data: {
        accessToken,
        refreshToken: newRefreshToken,
      },
    });

  } catch (error) {
    logger.error('Token refresh failed:', error);
    res.status(401).json({
      success: false,
      message: 'Invalid refresh token',
    });
  }
});

// Logout
router.post('/logout', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;

    // Remove refresh token from Redis
    await redis.del(`refresh_token:${userId}`);

    logger.info(`User logged out successfully: ${userId}`);

    res.json({
      success: true,
      message: 'Logged out successfully',
    });

  } catch (error) {
    logger.error('Logout failed:', error);
    res.status(500).json({
      success: false,
      message: 'Logout failed',
    });
  }
});

// Get current user profile
router.get('/profile', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;

    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        customerProfile: true,
        driverProfile: {
          include: {
            vehicles: true,
            documents: true,
          },
        },
        adminProfile: true,
      },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
      });
    }

    res.json({
      success: true,
      data: {
        id: user.id,
        email: user.email,
        phone: user.phone,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        status: user.status,
        profileImage: user.profileImage,
        emailVerified: user.emailVerified,
        phoneVerified: user.phoneVerified,
        language: user.language,
        profile: user.customerProfile || user.driverProfile || user.adminProfile,
        createdAt: user.createdAt,
        lastLoginAt: user.lastLoginAt,
      },
    });

  } catch (error) {
    logger.error('Get profile failed:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get profile',
    });
  }
});

export default router;
