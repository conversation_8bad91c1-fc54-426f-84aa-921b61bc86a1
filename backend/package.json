{"name": "ev-loader-backend", "version": "1.0.0", "description": "EV Loader Rental Platform Backend - Microservices Architecture", "main": "dist/server.js", "scripts": {"dev": "concurrently \"npm run auth:dev\" \"npm run booking:dev\" \"npm run fleet:dev\" \"npm run payment:dev\" \"npm run notification:dev\" \"npm run analytics:dev\"", "build": "tsc && npm run build:services", "build:services": "concurrently \"npm run auth:build\" \"npm run booking:build\" \"npm run fleet:build\" \"npm run payment:build\" \"npm run notification:build\" \"npm run analytics:build\"", "start": "node dist/server.js", "test": "jest --coverage", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "migrate": "npx prisma migrate deploy", "migrate:dev": "npx prisma migrate dev", "db:seed": "npx prisma db seed", "db:studio": "npx prisma studio", "auth:dev": "nodemon src/services/auth/server.ts", "auth:build": "tsc src/services/auth/**/*.ts --outDir dist/services/auth", "booking:dev": "nodemon src/services/booking/server.ts", "booking:build": "tsc src/services/booking/**/*.ts --outDir dist/services/booking", "fleet:dev": "nodemon src/services/fleet/server.ts", "fleet:build": "tsc src/services/fleet/**/*.ts --outDir dist/services/fleet", "payment:dev": "nodemon src/services/payment/server.ts", "payment:build": "tsc src/services/payment/**/*.ts --outDir dist/services/payment", "notification:dev": "nodemon src/services/notification/server.ts", "notification:build": "tsc src/services/notification/**/*.ts --outDir dist/services/notification", "analytics:dev": "nodemon src/services/analytics/server.ts", "analytics:build": "tsc src/services/analytics/**/*.ts --outDir dist/services/analytics", "docker:build": "docker build -t ev-loader-backend .", "docker:run": "docker run -p 3000:3000 ev-loader-backend"}, "dependencies": {"@prisma/client": "^5.7.1", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/node": "^20.10.5", "@types/socket.io": "^3.0.2", "aws-sdk": "^2.1518.0", "bcryptjs": "^2.4.3", "bull": "^4.12.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "firebase-admin": "^12.0.0", "helmet": "^7.1.0", "ioredis": "^5.3.2", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.7", "pg": "^8.11.3", "prisma": "^5.7.1", "razorpay": "^2.9.2", "redis": "^4.6.12", "socket.io": "^4.7.4", "twilio": "^4.19.3", "winston": "^3.11.0"}, "devDependencies": {"@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "concurrently": "^8.2.2", "eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "^5.3.3"}, "prisma": {"seed": "ts-node prisma/seed.ts"}, "engines": {"node": ">=18.0.0"}}