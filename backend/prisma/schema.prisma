// EV Loader Rental Platform Database Schema
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Management
model User {
  id                String    @id @default(cuid())
  email             String    @unique
  phone             String    @unique
  password          String
  firstName         String
  lastName          String
  role              UserRole
  status            UserStatus @default(ACTIVE)
  emailVerified     Boolean   @default(false)
  phoneVerified     Boolean   @default(false)
  profileImage      String?
  language          String    @default("en")
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  lastLoginAt       DateTime?
  
  // Relationships
  customerProfile   Customer?
  driverProfile     Driver?
  adminProfile      Admin?
  bookings          Booking[]
  driverBookings    Booking[] @relation("DriverBookings")
  reviews           Review[]
  notifications     Notification[]
  paymentMethods    PaymentMethod[]
  
  @@map("users")
}

model Customer {
  id              String    @id @default(cuid())
  userId          String    @unique
  user            User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  addresses       Address[]
  preferences     Json?
  totalBookings   Int       @default(0)
  totalSpent      Decimal   @default(0)
  averageRating   Float?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  
  @@map("customers")
}

model Driver {
  id                    String          @id @default(cuid())
  userId                String          @unique
  user                  User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  licenseNumber         String          @unique
  licenseExpiry         DateTime
  aadharNumber          String          @unique
  panNumber             String          @unique
  bankAccountNumber     String
  ifscCode              String
  isOnline              Boolean         @default(false)
  currentLocation       Json?
  totalEarnings         Decimal         @default(0)
  totalTrips            Int             @default(0)
  averageRating         Float?
  commissionRate        Float           @default(0.15)
  status                DriverStatus    @default(PENDING)
  verificationStatus    VerificationStatus @default(PENDING)
  documents             DriverDocument[]
  vehicles              Vehicle[]
  availabilitySchedule  Json?
  createdAt             DateTime        @default(now())
  updatedAt             DateTime        @updatedAt
  
  @@map("drivers")
}

model Admin {
  id          String   @id @default(cuid())
  userId      String   @unique
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  permissions Json
  department  String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@map("admins")
}

// Vehicle & Fleet Management
model Vehicle {
  id                  String        @id @default(cuid())
  driverId            String
  driver              Driver        @relation(fields: [driverId], references: [id], onDelete: Cascade)
  registrationNumber  String        @unique
  vehicleType         VehicleType
  brand               String
  model               String
  year                Int
  color               String
  capacity            Int           // in kg
  batteryCapacity     Float         // in kWh
  currentBatteryLevel Float?        // percentage
  range               Int           // in km
  chargingStatus      ChargingStatus @default(NOT_CHARGING)
  lastServiceDate     DateTime?
  nextServiceDue      DateTime?
  insuranceNumber     String
  insuranceExpiry     DateTime
  permitNumber        String?
  permitExpiry        DateTime?
  status              VehicleStatus @default(ACTIVE)
  currentLocation     Json?
  totalDistance       Float         @default(0)
  totalTrips          Int           @default(0)
  documents           VehicleDocument[]
  bookings            Booking[]
  maintenanceRecords  MaintenanceRecord[]
  createdAt           DateTime      @default(now())
  updatedAt           DateTime      @updatedAt
  
  @@map("vehicles")
}

// Booking System
model Booking {
  id                    String        @id @default(cuid())
  customerId            String
  customer              User          @relation(fields: [customerId], references: [id])
  driverId              String?
  driver                User?         @relation("DriverBookings", fields: [driverId], references: [id])
  vehicleId             String?
  vehicle               Vehicle?      @relation(fields: [vehicleId], references: [id])
  bookingNumber         String        @unique
  status                BookingStatus @default(PENDING)
  bookingType           BookingType   @default(IMMEDIATE)
  scheduledDateTime     DateTime?
  
  // Location Details
  pickupAddress         Json
  dropoffAddress        Json
  distance              Float?
  estimatedDuration     Int?          // in minutes
  
  // Load Details
  loadWeight            Float         // in kg
  loadCategory          LoadCategory
  loadDescription       String?
  specialInstructions   String?
  requiresHelper        Boolean       @default(false)
  
  // Pricing
  baseFare              Decimal
  distanceFare          Decimal
  timeFare              Decimal
  demandMultiplier      Float         @default(1.0)
  totalFare             Decimal
  platformFee           Decimal
  taxes                 Decimal
  finalAmount           Decimal
  
  // Tracking
  driverAcceptedAt      DateTime?
  driverArrivedAt       DateTime?
  pickupCompletedAt     DateTime?
  deliveryCompletedAt   DateTime?
  otp                   String?
  
  // Payment
  paymentStatus         PaymentStatus @default(PENDING)
  paymentMethod         String?
  transactionId         String?
  
  // Additional
  cancellationReason    String?
  cancelledBy           String?
  cancelledAt           DateTime?
  
  createdAt             DateTime      @default(now())
  updatedAt             DateTime      @updatedAt
  
  // Relationships
  payments              Payment[]
  reviews               Review[]
  trackingUpdates       TrackingUpdate[]
  
  @@map("bookings")
}

// Payment System
model Payment {
  id              String        @id @default(cuid())
  bookingId       String
  booking         Booking       @relation(fields: [bookingId], references: [id])
  userId          String
  user            User          @relation(fields: [userId], references: [id])
  amount          Decimal
  currency        String        @default("INR")
  paymentMethod   String
  gateway         PaymentGateway
  gatewayOrderId  String?
  gatewayPaymentId String?
  status          PaymentStatus @default(PENDING)
  failureReason   String?
  refundAmount    Decimal?
  refundStatus    RefundStatus?
  metadata        Json?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  
  @@map("payments")
}

model PaymentMethod {
  id          String            @id @default(cuid())
  userId      String
  user        User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  type        PaymentMethodType
  details     Json              // Encrypted payment details
  isDefault   Boolean           @default(false)
  isActive    Boolean           @default(true)
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt
  
  @@map("payment_methods")
}

// Review & Rating System
model Review {
  id          String     @id @default(cuid())
  bookingId   String     @unique
  booking     Booking    @relation(fields: [bookingId], references: [id])
  reviewerId  String
  reviewer    User       @relation(fields: [reviewerId], references: [id])
  rating      Int        // 1-5 stars
  comment     String?
  photos      String[]   // Array of photo URLs
  reviewType  ReviewType
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  
  @@map("reviews")
}

// Notification System
model Notification {
  id          String           @id @default(cuid())
  userId      String
  user        User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  title       String
  message     String
  type        NotificationType
  data        Json?
  isRead      Boolean          @default(false)
  sentAt      DateTime         @default(now())
  readAt      DateTime?
  
  @@map("notifications")
}

// Location & Tracking
model Address {
  id          String   @id @default(cuid())
  customerId  String
  customer    Customer @relation(fields: [customerId], references: [id], onDelete: Cascade)
  label       String
  address     String
  latitude    Float
  longitude   Float
  landmark    String?
  type        AddressType
  isDefault   Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@map("addresses")
}

model TrackingUpdate {
  id          String   @id @default(cuid())
  bookingId   String
  booking     Booking  @relation(fields: [bookingId], references: [id], onDelete: Cascade)
  latitude    Float
  longitude   Float
  timestamp   DateTime @default(now())
  speed       Float?
  heading     Float?
  accuracy    Float?
  
  @@map("tracking_updates")
}

// Document Management
model DriverDocument {
  id          String         @id @default(cuid())
  driverId    String
  driver      Driver         @relation(fields: [driverId], references: [id], onDelete: Cascade)
  type        DocumentType
  documentUrl String
  status      DocumentStatus @default(PENDING)
  expiryDate  DateTime?
  verifiedAt  DateTime?
  verifiedBy  String?
  rejectionReason String?
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt
  
  @@map("driver_documents")
}

model VehicleDocument {
  id          String         @id @default(cuid())
  vehicleId   String
  vehicle     Vehicle        @relation(fields: [vehicleId], references: [id], onDelete: Cascade)
  type        DocumentType
  documentUrl String
  status      DocumentStatus @default(PENDING)
  expiryDate  DateTime?
  verifiedAt  DateTime?
  verifiedBy  String?
  rejectionReason String?
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt
  
  @@map("vehicle_documents")
}

// Maintenance & Service
model MaintenanceRecord {
  id            String            @id @default(cuid())
  vehicleId     String
  vehicle       Vehicle           @relation(fields: [vehicleId], references: [id], onDelete: Cascade)
  type          MaintenanceType
  description   String
  cost          Decimal
  serviceCenter String
  scheduledDate DateTime
  completedDate DateTime?
  status        MaintenanceStatus @default(SCHEDULED)
  nextDueDate   DateTime?
  createdAt     DateTime          @default(now())
  updatedAt     DateTime          @updatedAt
  
  @@map("maintenance_records")
}

// Analytics & Reporting
model Analytics {
  id          String   @id @default(cuid())
  date        DateTime
  metric      String
  value       Float
  metadata    Json?
  createdAt   DateTime @default(now())
  
  @@map("analytics")
}

// Enums
enum UserRole {
  CUSTOMER
  DRIVER
  ADMIN
  SUPER_ADMIN
}

enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  DELETED
}

enum DriverStatus {
  PENDING
  ACTIVE
  INACTIVE
  SUSPENDED
  REJECTED
}

enum VerificationStatus {
  PENDING
  VERIFIED
  REJECTED
  EXPIRED
}

enum VehicleType {
  SMALL_EV
  MEDIUM_EV
  LARGE_EV
  ELECTRIC_BIKE
  ELECTRIC_SCOOTER
}

enum VehicleStatus {
  ACTIVE
  INACTIVE
  MAINTENANCE
  OUT_OF_SERVICE
}

enum ChargingStatus {
  CHARGING
  NOT_CHARGING
  FULLY_CHARGED
  LOW_BATTERY
}

enum BookingStatus {
  PENDING
  CONFIRMED
  DRIVER_ASSIGNED
  DRIVER_ARRIVED
  PICKUP_COMPLETED
  IN_TRANSIT
  DELIVERED
  COMPLETED
  CANCELLED
  REFUNDED
}

enum BookingType {
  IMMEDIATE
  SCHEDULED
}

enum LoadCategory {
  FURNITURE
  ELECTRONICS
  FOOD_ITEMS
  DOCUMENTS
  CLOTHING
  HOUSEHOLD_ITEMS
  INDUSTRIAL_GOODS
  FRAGILE_ITEMS
  HAZARDOUS_MATERIALS
  OTHER
}

enum PaymentStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  REFUNDED
  PARTIALLY_REFUNDED
}

enum PaymentGateway {
  RAZORPAY
  PAYTM
  PHONEPE
  GPAY
  CASH
}

enum PaymentMethodType {
  UPI
  CREDIT_CARD
  DEBIT_CARD
  NET_BANKING
  WALLET
  CASH
}

enum RefundStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
}

enum ReviewType {
  DRIVER_TO_CUSTOMER
  CUSTOMER_TO_DRIVER
}

enum NotificationType {
  BOOKING_CONFIRMED
  DRIVER_ASSIGNED
  DRIVER_ARRIVED
  PICKUP_COMPLETED
  DELIVERY_COMPLETED
  PAYMENT_RECEIVED
  RATING_RECEIVED
  PROMOTION
  SYSTEM_UPDATE
  MAINTENANCE_REMINDER
}

enum AddressType {
  HOME
  WORK
  OTHER
}

enum DocumentType {
  DRIVING_LICENSE
  AADHAR_CARD
  PAN_CARD
  VEHICLE_REGISTRATION
  INSURANCE_CERTIFICATE
  POLLUTION_CERTIFICATE
  PERMIT
  BANK_PASSBOOK
}

enum DocumentStatus {
  PENDING
  VERIFIED
  REJECTED
  EXPIRED
}

enum MaintenanceType {
  ROUTINE_SERVICE
  BATTERY_REPLACEMENT
  TIRE_REPLACEMENT
  BRAKE_SERVICE
  ELECTRICAL_REPAIR
  BODY_REPAIR
  EMERGENCY_REPAIR
}

enum MaintenanceStatus {
  SCHEDULED
  IN_PROGRESS
  COMPLETED
  CANCELLED
}
