{"name": "ev-loader-apps", "version": "1.0.0", "description": "EV Loader Delivery Service - Customer, Driver & Vendor Apps", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "build:android": "cd android && ./gradlew assembleRelease", "build:ios": "cd ios && xcodebuild -workspace EVLoaderApps.xcworkspace -scheme EVLoaderApps -configuration Release -destination generic/platform=iOS -archivePath EVLoaderApps.xcarchive archive", "customer:android": "react-native run-android --appId com.evloader.customer", "customer:ios": "react-native run-ios --scheme CustomerApp", "driver:android": "react-native run-android --appId com.evloader.driver", "driver:ios": "react-native run-ios --scheme DriverApp", "vendor:android": "react-native run-android --appId com.evloader.vendor", "vendor:ios": "react-native run-ios --scheme VendorApp"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.19.5", "@react-native-community/geolocation": "^3.2.1", "@react-native-firebase/app": "^18.6.2", "@react-native-firebase/auth": "^18.6.2", "@react-native-firebase/messaging": "^18.6.2", "@react-native-google-signin/google-signin": "^10.1.0", "@react-native-masked-view/masked-view": "^0.3.0", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "@reduxjs/toolkit": "^1.9.7", "react": "18.2.0", "react-i18next": "^13.5.0", "react-native": "0.72.7", "react-native-config": "^1.5.1", "react-native-gesture-handler": "^2.14.0", "react-native-linear-gradient": "^2.8.3", "react-native-maps": "^1.8.0", "react-native-permissions": "^4.0.0", "react-native-razorpay": "^2.3.0", "react-native-reanimated": "^3.6.1", "react-native-safe-area-context": "^4.7.4", "react-native-screens": "^3.27.0", "react-native-svg": "^14.0.0", "react-native-vector-icons": "^10.0.2", "react-redux": "^8.1.3", "socket.io-client": "^4.7.4"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}}