{"name": "ev-loader-rental-platform", "version": "1.0.0", "description": "Three-tier EV Loader Rental Platform - Customer, Driver & Admin", "main": "index.js", "workspaces": ["backend", "customer-app", "driver-app", "admin-dashboard"], "scripts": {"dev": "concurrently \"npm run backend:dev\" \"npm run customer:dev\" \"npm run driver:dev\" \"npm run admin:dev\"", "backend:dev": "cd backend && npm run dev", "customer:dev": "cd customer-app && npm start", "driver:dev": "cd driver-app && npm start", "admin:dev": "cd admin-dashboard && npm start", "build": "npm run backend:build && npm run customer:build && npm run driver:build && npm run admin:build", "test": "npm run backend:test && npm run customer:test && npm run driver:test && npm run admin:test", "setup": "npm install && npm run setup:backend && npm run setup:apps", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.19.5", "@react-native-community/geolocation": "^3.2.1", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "react": "18.3.1", "react-i18next": "^13.5.0", "react-native": "0.72.7", "react-native-gesture-handler": "^2.14.0", "react-native-linear-gradient": "^2.8.3", "react-native-maps": "^1.15.0", "react-native-reanimated": "^3.6.1", "react-native-safe-area-context": "^4.7.4", "react-native-screens": "^3.27.0", "react-native-svg": "^14.0.0", "react-native-vector-icons": "^10.0.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}}