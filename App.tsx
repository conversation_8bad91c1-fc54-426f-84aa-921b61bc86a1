import React, { useEffect } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { StatusBar, Platform } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';

// Import i18n configuration
import './src/i18n';

// Import navigation
import CustomerNavigator from './src/navigation/CustomerNavigator';

// Import theme
import { colors } from './src/theme';

const App: React.FC = () => {
  useEffect(() => {
    // Set status bar style
    if (Platform.OS === 'android') {
      StatusBar.setBackgroundColor(colors.primary, true);
      StatusBar.setBarStyle('light-content', true);
    }
  }, []);

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <SafeAreaProvider>
        <NavigationContainer>
          <StatusBar
            barStyle="light-content"
            backgroundColor={colors.primary}
            translucent={false}
          />
          <CustomerNavigator />
        </NavigationContainer>
      </SafeAreaProvider>
    </GestureHandlerRootView>
  );
};

export default App;
